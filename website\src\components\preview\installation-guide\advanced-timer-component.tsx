import {
  ModernCloc,
  ClocLoginDialog,
  ClocThemeToggle,
  DailyActivityDisplayer,
  WorkedProjectDisplayer,
  WeeklyWorkedTimeDisplayer,
  DailyWorkedTimeDisplayer,
  ClocLanguageSwitch,
  BasicClocReport,
  ClocAppsUrlList,
} from "@cloc/atoms";

function TimerDashboard() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <header className="flex justify-between items-center">
        <h1 className="text-3xl font-bold p-0 m-0">Dashboard</h1>
        <div className="flex items-center justify-center space-x-4">
          <ClocThemeToggle />
          <ClocLanguageSwitch />
          <ClocLoginDialog />
        </div>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="col-span-1 flex flex-col gap-2">
          <DailyActivityDisplayer />
          <WorkedProjectDisplayer />
          <WeeklyWorkedTimeDisplayer />
          <DailyWorkedTimeDisplayer />
        </div>

        <div className="col-span-1 md:col-span-2 lg:col-span-1">
          <ModernCloc
            className="min-w-[300px]"
            expanded={true}
            showProgress={true}
          />
        </div>
      </div>
      <ClocAppsUrlList />
      <BasicClocReport type="line" />
    </div>
  );
}

export default function AdvancedTimerComponent() {
  return <TimerDashboard />;
}
