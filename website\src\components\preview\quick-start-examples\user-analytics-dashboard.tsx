import {
  ClocTrackingFilter,
  ClocTrackingClickInsight,
  ClocTrackingSessionInsight,
  ClocTrackingHeatmap,
} from "@cloc/atoms";

export default function UserAnalyticsDashboard() {
  return (
    <div className=" bg-gray-50 dark:bg-gray-950 p-2">
      <div className="max-w-7xl mx-auto flex flex-col gap-4">
        <h1 className="text-3xl font-bold">User Analytics Dashboard</h1>

        {/* Filter Section */}

        <ClocTrackingFilter autoRefresh={true} refreshInterval={60000} />

        {/* Analytics Grid */}

        <ClocTrackingClickInsight />

        <ClocTrackingSessionInsight />

        {/* Heatmap Section */}

        <ClocTrackingHeatmap className="w-full" showControl={true} />
      </div>
    </div>
  );
}
