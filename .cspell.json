{"version": "0.2", "language": "en", "caseSensitive": false, "$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "words": ["clsx", "cmfcmf", "Gitter", "noopener", "octicon", "typecheck", "Userspage"], "useGitignore": true, "ignorePaths": [".cicleci/*", ".github/*", ".git/*", ".git/!{COMMIT_EDITMSG,EDITMSG}", ".git/*/**", ".yarn", "**/.git/**", ".vscode", ".giti<PERSON>re", "action/lib/**", "coverage", ".cspell.json", "cspell.json", ".gitbook.yaml", ".whitesource", "**/node_modules/**", "**/.docus<PERSON>/**", "/**/docs/i18n/**", "**/build/**", "**/vscode-extension/**", "/**/package-lock.json", "/**/yarn.lock", "**/assets/i18n/*.json", "**/*.svg", "docker-compose.yml", "Dockerfile", "/**/*.{svg,css,scss}", "docs/i18n/**", "docs/assets/**"]}