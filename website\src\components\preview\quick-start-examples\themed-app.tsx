import { ThemeToggle, ModernCloc, BasicClocReport } from "@cloc/atoms";

export default function ThemedApp() {
  return (
    <div className="flex bg-background text-foreground">
      {/* Theme Toggle Button */}
      <div className="p-4  max-w-fit justify-end">
        <ThemeToggle />
      </div>

      {/* Components automatically adapt to theme */}
      <div className=" p-3 space-y-6">
        <ModernCloc
          className="max-w-[300px]"
          expanded={true}
          showProgress={true}
          variant="bordered"
        />
        <BasicClocReport type="line" variant="default" />
      </div>
    </div>
  );
}
