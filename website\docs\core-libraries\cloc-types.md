---
id: cloc-types
title: Cloc Types
sidebar_label: Cloc Types
sidebar_position: 7
---

# @cloc/types Library Documentation

@cloc/types is a comprehensive TypeScript definition package that provides type safety and IntelliSense support for the Cloc ecosystem. It serves as the foundation for type definitions across all Cloc components, utilities, and features.

## Installation

```bash
# Using npm
npm install @cloc/types

# Using yarn
yarn add @cloc/types

# Using pnpm
pnpm add @cloc/types

```

## Core Type Definitions

### 1. Timer Types

```tsx
interface ITime {
	hours: number;
	minutes: number;
	seconds: number;
}

interface TimeValues {
	days: number;
	hours: number;
	minutes: number;
	seconds: number;
	milliseconds: number;
	totalSeconds: number;
}
```

The timer types provide structured interfaces for handling time-related data. `ITime` offers basic time units while `TimeValues` extends this with additional precision and calculations.

### 2. Timer Status Types

```tsx
interface ITimerStatus {
	duration: number;
	running: boolean;
	lastLog: {
		taskId: string;
		projectId: string;
		organizationTeamId: string;
	} | null;
}
```

This interface manages the timer's current state, tracking duration, running status, and associated task information.

### 3. Organization and Team Types

```tsx
interface IOrganizationTeamList {
	members: OT_Member[];
	// Additional team properties
}

interface OT_Member {
	id: string;
	fullName: string;
	// Additional member properties
}
```

These types define the structure for organization teams and their members, essential for team management features.

### 4. Theme Configuration

```tsx
interface ITheme extends Theme<{}> {
	colors: {
		textColor: string;
		backgroundColor: string;
		primaryColor: string;
		secondaryColor: string;
	};
}
```

The theme interface extends the base Theme type and provides specific color configurations for consistent styling.

### 5. Employee Interface

```tsx
interface IEmployee {
	fullName: string;
	id: string;
	isActive: boolean;
	isArchived: boolean;
	tenantId: string | null;
	organizationId: string | null;
	// Additional employee properties
}
```

This interface defines the structure for employee data, including essential information and organizational relationships.

## Compatibility

- React: ^16.9.0 - ^18.x
- Next.js: ^12.x - ^14.x

## Best Practices

1. **Type Safety**
   - Use strict type checking with `strict: true` in tsconfig
   - Avoid using `any` type
   - Leverage union types for better type safety
2. **Component Props**
   - Define clear interfaces for component props
   - Use optional properties when appropriate
   - Document complex prop types with JSDoc comments
3. **State Management**
   - Define types for state objects
   - Use discriminated unions for complex state
   - Maintain consistent naming conventions
4. **API Integration**
   - Define interfaces for API responses
   - Use generics for reusable API types
   - Include error handling types

## Common Patterns

### Timer Component Implementation

```tsx
import { ITime, TimeValues } from "@cloc/types";

const Timer: React.FC<ITime> = ({ hours, minutes, seconds }) => {
	// Implementation
};
```

### Theme Usage

```tsx
import { ITheme } from "@cloc/types";

const theme: ITheme = {
	colors: {
		textColor: "#000000",
		backgroundColor: "#FFFFFF",
		primaryColor: "#007AFF",
		secondaryColor: "#5856D6",
	},
};
```
