---
id: examples-tutorials
title: Examples & Tutorials
sidebar_label: Examples & Tutorials
sidebar_position: 7
---

## Basic Setup

### 1. Installation

First, install the core CLOC packages and their peer dependencies:

```bash
# Install core packages
npm install @cloc/atoms @cloc/ui @cloc/types

# Install peer dependencies
npm install theme-ui @emotion/react

```

### 2. Provider Setup

The ClocProvider is the foundation of all CLOC components. It provides theming, authentication, and timer functionality to all child components:

```tsx
import { ClocProvider, defaultTheme } from "@cloc/atoms";

function App() {
	return (
		<ClocProvider theme={defaultTheme} token={yourAccessToken}>
			{/* Your app content */}
		</ClocProvider>
	);
}
```

The provider accepts an optional access token for authentication and a theme object for customization.

## Timer Components

### 1. Basic Timer

The BasicTimer component offers a clean, minimalist timer interface with various styling options:

```tsx
import { BasicTimer, ClocProvider } from "@cloc/atoms";

export function TimerExample() {
	return (
		<ClocProvider>
			<BasicTimer
				border="thick"
				background="primary"
				color="secondary"
				rounded="large"
				readonly={false}
				icon={true}
			/>
		</ClocProvider>
	);
}
```

Key features:

- Customizable borders and corners
- Color themes
- Optional icon display
- Read-only mode
- Progress tracking

### 2. Modern Timer

The ModernCloc component provides a more sophisticated timer with additional features:

```tsx
import { ModernCloc, ClocProvider } from "@cloc/atoms";

export function ModernTimerExample() {
	return (
		<ClocProvider>
			<ModernCloc
				variant="bordered"
				size="default"
				showProgress={true}
				expanded={true}
				draggable={true}
				resizable={true}
				separator=":"
			/>
		</ClocProvider>
	);
}
```

Features:

- Progress bar visualization
- Expandable interface
- Drag and resize capabilities
- Multiple size variants
- Custom time separators

### 3. Custom Timer

For more specialized needs, the ClocCustom component offers additional customization:

```tsx
import { ClocCustom, ClocProvider } from "@cloc/atoms";

export function CustomTimerExample() {
	return (
		<ClocProvider>
			<ClocCustom
				background="secondary"
				color="primary"
				icon={true}
				labeled={true}
				rounded="small"
			/>
		</ClocProvider>
	);
}
```

## Chart Components

### 1. Basic Charts

CLOC provides various chart types for data visualization:

```tsx
import { ClocChart, ClocProvider } from "@cloc/atoms";

export function ChartExample() {
	return (
		<ClocProvider>
			{/* Bar Chart */}
			<ClocChart type="bar" className="min-w-[400px]" />

			{/* Line Chart */}
			<ClocChart type="line" className="min-w-[400px]" />

			{/* Area Chart */}
			<ClocChart type="area" className="min-w-[400px]" />

			{/* Radar Chart */}
			<ClocChart type="radar" className="min-w-[400px]" />
		</ClocProvider>
	);
}
```

Available chart types:

- bar
- bar-vertical
- line
- area
- radar
- radial
- tooltip

### 2. Chart with Custom Data

You can provide custom data and configuration to charts:

```tsx
import { ClocChart, ClocProvider } from "@cloc/atoms";

const chartConfig = {
	cedric: { label: "Cedric", color: "#4CAF50" },
	salva: { label: "Salva", color: "#2196F3" },
	josh: { label: "Josh", color: "#FFC107" },
	ndeko: { label: "Ndeko", color: "#9C27B0" },
};

export function CustomChartExample() {
	return (
		<ClocProvider>
			<ClocChart type="bar" config={chartConfig} className="min-w-[400px]" />
		</ClocProvider>
	);
}
```

Compatibility:

- React 18.0.0 and above
- Next.js 13.0.0 and above
- Theme UI 0.15.0 and above

All components are built with TypeScript and include proper type definitions for excellent IDE support and type safety.
