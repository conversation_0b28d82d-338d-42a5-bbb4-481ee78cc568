{"id": "core-libraries/cloc-api", "title": "Cloc API", "description": "@cloc/api is a powerful TypeScript library that provides seamless integration with Cloc backend services. It offers a comprehensive suite of async functions for time tracking, organization management, authentication, and reporting functionalities. Built with TypeScript, it ensures type safety and excellent developer experience.", "source": "@site/docs/core-libraries/cloc-api.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-api", "permalink": "/docs/core-libraries/cloc-api", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-api.md", "tags": [], "version": "current", "sidebarPosition": 8, "frontMatter": {"id": "cloc-api", "title": "Cloc API", "sidebar_label": "Cloc API", "sidebar_position": 8}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc Types", "permalink": "/docs/core-libraries/cloc-types"}, "next": {"title": "Cloc Tracking", "permalink": "/docs/core-libraries/cloc-tracking"}}