---
id: reports
title: Reports
sidebar_label: Reports
sidebar_position: 7
---

# Working Hours Reports

The Working Hours Reports feature provides various chart visualizations to help track and analyze working hours across team members. These charts are built using Recharts and offer different ways to view time-tracking data.

## Available Chart Types

### Bar Charts

Bar charts provide a clear comparison of working hours, available in both vertical and horizontal orientations.

#### Horizontal Bar Chart

![Horizontal Bar Chart](../assets/component/reports_working-hours_bar-chart-horizontal.png)

```tsx
import { BasicClocReport } from "@cloc/atoms";
function HorizontalBarExample() {
  return (
    <ClocProvider>
      <BasicClocReport type="bar" />
    </ClocProvider>
  );
}
```

The horizontal bar chart displays working hours with bars extending horizontally, making it easy to compare values across different team members.

📚 [View Implementation Demo here](https://kit-examples-base.cloc.ai/components/timer/modern-timer)

#### Vertical Bar Chart

![Vertical Bar Chart](../assets/component/reports_working-hours_bar-chart-vertical.png)

```tsx
import { BasicClocReport } from "@cloc/atoms";
function VerticalBarExample() {
  return (
    <ClocProvider>
      <BasicClocReport type="bar-vertical" />
    </ClocProvider>
  );
}
```

Vertical bar charts present the same data with bars extending vertically, which can be preferable when dealing with time-series data.

### Line Charts

Line charts are excellent for showing trends over time and come in two variants.

#### Basic Line Chart

![Line Chart](../assets/component/reports_working-hours_line-chart.png)

```tsx
import { BasicClocReport } from "@cloc/atoms";
function LineChartExample() {
  return (
    <ClocProvider>
      <BasicClocReport type="line" />
    </ClocProvider>
  );
}
```

The basic line chart connects data points with straight lines, making it easy to spot trends and patterns in working hours.

#### Area Chart

![Area Chart](../assets/component/reports_working-hours_area-chart.png)

```tsx
import { BasicClocReport } from "@cloc/atoms";
function LineChartExample() {
  return (
    <ClocProvider>
      <BasicClocReport type="area" />
    </ClocProvider>
  );
}
```

Area charts fill the space below the line, providing a visual emphasis on the volume of hours worked.

#### Tooltip Chart

![Tooltip Chart](../assets/component/reports_working-hours_tooltip-chart.png)

```tsx
import { BasicClocReport } from "@cloc/atoms";
function LineChartExample() {
  return (
    <ClocProvider>
      <BasicClocReport type="radar" />
    </ClocProvider>
  );
}
```

The Tooltip Chart is a specialized chart component that combines bar chart visualization with enhanced tooltip interactions for detailed data viewing.

#### Radar Chart

![Radar Chart](../assets/component/reports_working-hours_radar-chart.png)

```tsx
import { BasicClocReport } from "@cloc/atoms";
function LineChartExample() {
  return (
    <ClocProvider>
      <BasicClocReport type="radar" />
    </ClocProvider>
  );
}
```

Radar charts display multivariate data on a two-dimensional chart with three or more variables, useful for comparing multiple team members' working patterns.

#### Radial Chart

![Radial Chart](../assets/component/reports_working-hours_radial-chart.png)

```tsx
import { BasicClocReport } from "@cloc/atoms";
function LineChartExample() {
  return (
    <ClocProvider>
      <BasicClocReport type="radial" />
    </ClocProvider>
  );
}
```

Radial charts present data in a circular format, making them ideal for displaying cyclical patterns in working hours.

## Role-Based Access

The BasicClocReport component implements role-based access control to manage report visibility:

### Manager Access

Managers can view reports for all employees within their organization. The component automatically:

- Verifies user permissions and roles
- Displays organization-wide employee data
- Enables filtering and grouping by team/department

```tsx
import { BasicClocReport } from "@cloc/atoms";

function ManagerReportExample() {
  return (
    <ClocProvider>
      {/* Manager view - shows all employee data */}
      <BasicClocReport type="bar" />
    </ClocProvider>
  );
}
```

### Employee Access

Regular employees can only view their own reports. The component:

- Restricts data visibility to personal records
- Maintains the same visualization options
- Filters out other employee data automatically

```tsx
import { BasicClocReport } from "@cloc/atoms";

function EmployeeReportExample() {
  return (
    <ClocProvider>
      {/* Employee view - shows only personal data */}
      <BasicClocReport type="bar" />
    </ClocProvider>
  );
}
```

> **Note**: The role-based access is handled automatically based on the authenticated user's permissions. No additional configuration is required.

## Common Props

All chart components accept the following common props:

| Prop      | Type                      | Default     | Description                      |
| --------- | ------------------------- | ----------- | -------------------------------- |
| type      | `ChartType`               | `'bar'`     | The type of chart to display     |
| variant   | `'default' \| 'bordered'` | `'default'` | The visual style of the chart    |
| className | `string`                  | `undefined` | Additional CSS classes           |
| draggable | `boolean`                 | `false`     | Whether the chart can be dragged |

## Data Structure

The charts expect data in the following format:

```tsx
type ChartData = {
  day: string;
  [key: string]: number | string;
}[];
```

Example data structure:

```tsx
const data = [
  { day: "Monday", john: 5, jane: 7, bob: 2 },
  { day: "Tuesday", john: 8, jane: 4, bob: 7 },
];
```

## Customization

Charts can be customized using the variant prop and additional className:

```tsx
<BasicClocReport type="line" variant="bordered" className="min-h-[400px]" />
```

For more examples and implementations, check out our [example repository](https://github.com/ever-co/ever-cloc) or visit our [live demo site](https://kit-examples-base.cloc.ai).

## Browser Support

The charts are supported in all modern browsers that support SVG and React:

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance Considerations

When dealing with large datasets, consider the following:

1. Limit the number of data points to maintain smooth rendering
2. Use appropriate chart types for your data size
3. Implement pagination or data windowing for historical data
