import {
  ModernCloc,
  BasicClocReport,
  useCloc<PERSON>ontext,
  ClocProvider,
} from "@cloc/atoms";

function ProductivityMonitor() {
  const { todayTrackedTime, isRunning } = useClocContext();

  return (
    <div className="max-w-6xl mx-auto p-3">
      <div className="flex flex-col gap-6">
        {/* Active Timer */}

        <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6">
          <h3 className="font-semibold mb-4">Current Session</h3>
          <ModernCloc
            expanded={true}
            showProgress={true}
            variant="default"
            size="default"
          />

          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-950 rounded">
            <p className="text-sm text-gray-600">
              Status: {isRunning ? "Active" : "Paused"}
            </p>
            <p className="text-sm text-gray-600">
              Today: {todayTrackedTime.hours}h {todayTrackedTime.minutes}m
            </p>
          </div>
        </div>

        {/* Analytics Charts */}

        <div className="flex flex-col gap-4">
          <BasicClocReport type="line" size="sm" />
          <BasicClocReport type="bar-vertical" size="sm" />
          <BasicClocReport type="area" size="sm" />
          <BasicClocReport type="pie" size="sm" />
        </div>
      </div>
    </div>
  );
}

// Wrap with provider
export default function ProductivityApp() {
  return <ProductivityMonitor />;
}
