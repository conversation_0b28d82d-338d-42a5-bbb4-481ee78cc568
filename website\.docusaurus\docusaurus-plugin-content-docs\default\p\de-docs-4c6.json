{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"tutorialSidebar": [{"type": "link", "label": "Introduction", "href": "/de/docs/index", "docId": "index", "unlisted": false}, {"type": "link", "label": "Cloc SDK", "href": "/de/docs/cloc-sdk", "docId": "cloc-sdk", "unlisted": false}, {"type": "category", "label": "Get Started", "collapsible": true, "collapsed": true, "items": [{"type": "link", "label": "Installation Guide", "href": "/de/docs/introduction/installation-guide", "docId": "introduction/installation-guide", "unlisted": false}, {"type": "link", "label": "Quick Start Examples", "href": "/de/docs/introduction/quick-start-example", "className": "docs-sidebar-quick-start", "docId": "introduction/quick-start-example", "unlisted": false}], "href": "/de/docs/introduction/"}, {"type": "category", "label": "Core Libraries", "collapsible": true, "collapsed": true, "items": [{"type": "link", "label": "Cloc Atoms", "href": "/de/docs/core-libraries/cloc-atoms", "docId": "core-libraries/cloc-atoms", "unlisted": false}, {"type": "link", "label": "Cloc UI", "href": "/de/docs/core-libraries/cloc-ui", "docId": "core-libraries/cloc-ui", "unlisted": false}, {"type": "link", "label": "Cloc Types", "href": "/de/docs/core-libraries/cloc-types", "docId": "core-libraries/cloc-types", "unlisted": false}, {"type": "link", "label": "Cloc API", "href": "/de/docs/core-libraries/cloc-api", "docId": "core-libraries/cloc-api", "unlisted": false}, {"type": "link", "label": "Cloc Tracking", "href": "/de/docs/core-libraries/cloc-tracking", "docId": "core-libraries/cloc-tracking", "unlisted": false}], "href": "/de/docs/core-libraries/"}, {"type": "category", "label": "Components Configuration", "collapsible": true, "collapsed": true, "items": [{"type": "link", "label": "Timer Components", "href": "/de/docs/components/timer", "docId": "components/timer", "unlisted": false}, {"type": "link", "label": "Reports", "href": "/de/docs/components/reports", "docId": "components/reports", "unlisted": false}, {"type": "link", "label": "UI Components", "href": "/de/docs/components/ui", "docId": "components/ui", "unlisted": false}], "href": "/de/docs/components/"}, {"type": "category", "label": "API Reference", "collapsible": true, "collapsed": true, "items": [{"type": "link", "label": "API Reference", "href": "/de/docs/api/api-reference", "docId": "api/api-reference", "unlisted": false}, {"type": "link", "label": "API Documentations", "href": "/de/docs/api/api-docs", "docId": "api/api-docs", "unlisted": false}], "href": "/de/docs/api/"}, {"type": "link", "label": "Examples & Tutorials", "href": "/de/docs/examples-tutorials", "docId": "examples-tutorials", "unlisted": false}]}, "docs": {"api/api": {"id": "api/api", "title": "API Reference", "description": "", "sidebar": "tutorialSidebar"}, "api/api-docs": {"id": "api/api-docs", "title": "API Documentations", "description": "1. Authentication Endpoints", "sidebar": "tutorialSidebar"}, "api/api-reference": {"id": "api/api-reference", "title": "API Reference", "description": "Core Components", "sidebar": "tutorialSidebar"}, "cloc-sdk": {"id": "cloc-sdk", "title": "Cloc SDK", "description": "Transform your productivity applications with the Ever Cloc SDK, a comprehensive development toolkit designed for building sophisticated time tracking and productivity solutions. Whether you're creating focused work timers, project management systems, or enterprise-grade productivity suites, our SDK provides the professional-grade building blocks you need.", "sidebar": "tutorialSidebar"}, "components/components": {"id": "components/components", "title": "Components Configuration", "description": "", "sidebar": "tutorialSidebar"}, "components/reports": {"id": "components/reports", "title": "Reports", "description": "The Working Hours Reports feature provides various chart visualizations to help track and analyze working hours across team members. These charts are built using Recharts and offer different ways to view time-tracking data.", "sidebar": "tutorialSidebar"}, "components/timer": {"id": "components/timer", "title": "Timer Components", "description": "The Timer Components library provides a comprehensive suite of customizable timing solutions for React and Next.js applications. Built with TypeScript and modern React patterns, these components offer flexibility, performance, and seamless integration capabilities.", "sidebar": "tutorialSidebar"}, "components/ui": {"id": "components/ui", "title": "UI Components", "description": "I'll create a detailed documentation for each UI component with thorough explanations.", "sidebar": "tutorialSidebar"}, "core-libraries/cloc-api": {"id": "core-libraries/cloc-api", "title": "Cloc API", "description": "@cloc/api is a powerful TypeScript library that provides seamless integration with Cloc backend services. It offers a comprehensive suite of async functions for time tracking, organization management, authentication, and reporting functionalities. Built with TypeScript, it ensures type safety and excellent developer experience.", "sidebar": "tutorialSidebar"}, "core-libraries/cloc-atoms": {"id": "core-libraries/cloc-atoms", "title": "Cloc Atoms", "description": "@cloc/atoms is a comprehensive React component library specializing in time tracking and data visualization. Built with TypeScript and modern React practices, it offers highly customizable components with built-in theme support, responsive design, and seamless integration capabilities.", "sidebar": "tutorialSidebar"}, "core-libraries/cloc-tracking": {"id": "core-libraries/cloc-tracking", "title": "Cloc Tracking", "description": "An analytics library that uses web page interactions to generate aggregated insights based on Microsoft Clarity.", "sidebar": "tutorialSidebar"}, "core-libraries/cloc-types": {"id": "core-libraries/cloc-types", "title": "Cloc Types", "description": "@cloc/types is a comprehensive TypeScript definition package that provides type safety and IntelliSense support for the Cloc ecosystem. It serves as the foundation for type definitions across all Cloc components, utilities, and features.", "sidebar": "tutorialSidebar"}, "core-libraries/cloc-ui": {"id": "core-libraries/cloc-ui", "title": "Cloc UI", "description": "@cloc/ui is a comprehensive React component library that provides the foundational building blocks for creating consistent, accessible, and themeable user interfaces across Cloc applications. The library emphasizes reusability, accessibility, and modern design principles.", "sidebar": "tutorialSidebar"}, "core-libraries/core-libraries": {"id": "core-libraries/core-libraries", "title": "Core Libraries", "description": "", "sidebar": "tutorialSidebar"}, "examples-tutorials": {"id": "examples-tutorials", "title": "Examples & Tutorials", "description": "Basic Setup", "sidebar": "tutorialSidebar"}, "index": {"id": "index", "title": "Introduction", "description": "Welcome to Ever Cloc, the comprehensive open-source platform that revolutionizes how teams track time, monitor productivity, and gain real-time insights into their work patterns. Built for modern organizations that value transparency, efficiency, and data-driven decision making.", "sidebar": "tutorialSidebar"}, "introduction/get-started": {"id": "introduction/get-started", "title": "Get Started", "description": "", "sidebar": "tutorialSidebar"}, "introduction/installation-guide": {"id": "introduction/installation-guide", "title": "Installation Guide", "description": "Welcome to Cloc SDK! This comprehensive guide will help you integrate powerful time tracking and analytics capabilities into your application. Whether you're building a productivity tool, project management system, or team collaboration platform, Cloc SDK provides the building blocks you need.", "sidebar": "tutorialSidebar"}, "introduction/quick-start-example": {"id": "introduction/quick-start-example", "title": "Quick Start Examples", "description": "This guide provides comprehensive examples for getting started with the Cloc SDK, from basic timer components to advanced analytics and tracking features.", "sidebar": "tutorialSidebar"}}}}