{"id": "components/ui", "title": "UI Components", "description": "I'll create a detailed documentation for each UI component with thorough explanations.", "source": "@site/docs/components/ui.md", "sourceDirName": "components", "slug": "/components/ui", "permalink": "/docs/components/ui", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/components/ui.md", "tags": [], "version": "current", "sidebarPosition": 8, "frontMatter": {"id": "ui", "title": "UI Components", "sidebar_label": "UI Components", "sidebar_position": 8}, "sidebar": "tutorialSidebar", "previous": {"title": "Reports", "permalink": "/docs/components/reports"}, "next": {"title": "API Reference", "permalink": "/docs/api/"}}