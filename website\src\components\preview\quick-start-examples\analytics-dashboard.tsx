import {
  ClocTrackingFilter,
  ClocTrackingHeatmap,
  ClocTrackingClickInsight,
  ClocTrackingSessionInsight,
} from "@cloc/atoms";

export default function AnalyticsDashboard() {
  return (
    <div className="space-y-6 ">
      {/* Filter Controls */}
      <ClocTrackingFilter autoRefresh={true} refreshInterval={30000} />

      {/* Analytics Grid */}
      <div className="grid  gap-6">
        <ClocTrackingClickInsight className="w-full" />
        <ClocTrackingSessionInsight className="w-full" />
      </div>

      {/* Heatmap Visualization */}
      <ClocTrackingHeatmap className="w-full " showControl={true} />
    </div>
  );
}
