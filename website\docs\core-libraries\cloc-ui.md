---
id: cloc-ui
title: Cloc UI
sidebar_label: Cloc UI
sidebar_position: 6
---

# @cloc/ui

@cloc/ui is a comprehensive React component library that provides the foundational building blocks for creating consistent, accessible, and themeable user interfaces across Cloc applications. The library emphasizes reusability, accessibility, and modern design principles.

## Installation

```bash

# Using npm
npm install @cloc/ui

# Using yarn
yarn add @cloc/ui

# Using pnpm
pnpm add @cloc/ui

```

## Core Components

### 1. Layout Components

## ClocLogo

A flexible logo component with multiple variants and sizes.

```tsx
import { ClocLogo } from "@cloc/ui";

function HeaderExample() {
	return (
		<ClocLogo
			variant="primary" // primary, monochrome, or inverse
			size="medium" // small, medium, or large
			animated={false} // Optional animation
		/>
	);
}
```

## MultiSelect

A powerful selection component supporting multiple selections with search and filtering capabilities.

```tsx
import { MultiSelect } from "@cloc/ui";

function SelectExample() {
	return (
		<MultiSelect
			options={[
				{ label: "Option 1", value: "1" },
				{ label: "Option 2", value: "2" },
			]}
			onChange={(values) => console.log("Selected:", values)}
			placeholder="Select items..."
			searchable={true}
			clearable={true}
		/>
	);
}
```

## Toaster

A system for displaying toast notifications with configurable positions and behaviors.

```tsx
import { Toaster, useToast } from "@cloc/ui";

function App() {
	const { toast } = useToast();

	return (
		<>
			<button
				onClick={() =>
					toast({
						title: "Success",
						description: "Operation completed",
						variant: "success",
					})
				}
			>
				Show Toast
			</button>
			<Toaster position="bottom-right" />
		</>
	);
}
```

## Calendar

A flexible calendar component supporting various date selection modes and formatting options.

```tsx
import { Calendar } from "@cloc/ui";

function CalendarExample() {
	return (
		<Calendar
			mode="single" // single, multiple, range
			selected={new Date()}
			onSelect={(date) => console.log("Selected:", date)}
			disabled={(date) => date < new Date()} // Disable past dates
		/>
	);
}
```

### 2. Form Elements

## Input

Text input component with various styles and states.

```tsx
import { Input } from "@cloc/ui";

function InputExample() {
	return (
		<Input
			type="text" // text, email, password, etc.
			placeholder="Enter text"
			value={value}
			onChange={handleChange}
			error={error} // Error state
			disabled={false} // Disabled state
			prefix={<SearchIcon />} // Optional prefix icon
			suffix={<ClearIcon />} // Optional suffix icon
		/>
	);
}
```

## Checkbox

Customizable checkbox component with support for indeterminate state.

```tsx
import { Checkbox } from "@cloc/ui";

function CheckboxExample() {
	return (
		<Checkbox
			checked={checked}
			onChange={handleChange}
			label="Enable feature"
			indeterminate={false} // Optional indeterminate state
			disabled={false} // Optional disabled state
		/>
	);
}
```

## Select

Dropdown selection component with single and multiple selection modes.

```tsx
import { Select } from "@cloc/ui";

function SelectExample() {
	return (
		<Select
			options={options}
			value={selectedValue}
			onChange={handleChange}
			placeholder="Select an option"
			isSearchable={true} // Enable search functionality
			isClearable={true} // Allow clearing selection
			isMulti={false} // Enable multiple selections
		/>
	);
}
```

## Label

Accessible form label component with optional required indicator.

```tsx
import { Label } from "@cloc/ui";

function LabelExample() {
	return (
		<Label
			htmlFor="input-id"
			required={true} // Shows required indicator
			optional={false} // Shows optional indicator
		>
			Username
		</Label>
	);
}
```

## Button

Versatile button component with multiple variants and states.

```tsx
import { Button } from "@cloc/ui";

function ButtonExample() {
	return (
		<Button
			variant="primary" // primary, secondary, outline, ghost
			size="medium" // small, medium, large
			loading={false} // Shows loading spinner
			disabled={false} // Disabled state
			leftIcon={<IconName />} // Optional left icon
			rightIcon={<IconName />} // Optional right icon
		>
			Click me
		</Button>
	);
}
```

### 3. Feedback Components

## Toast Notifications

System for displaying temporary notifications with various styles and positions.

```tsx
import { useToast } from "@cloc/ui";

function ToastExample() {
	const { toast } = useToast();

	const showToast = () => {
		toast({
			title: "Success",
			description: "Operation completed successfully",
			variant: "success", // success, error, warning, info
			duration: 5000, // Duration in milliseconds
			action: {
				// Optional action button
				label: "Undo",
				onClick: () => console.log("Undo clicked"),
			},
		});
	};
}
```

## Loading Indicators

Components for showing loading states.

```tsx
import { Spinner, LoadingOverlay } from "@cloc/ui";

function LoadingExample() {
	return (
		<>
			<Spinner size="medium" />
			<LoadingOverlay loading={isLoading} text="Loading data..." />
		</>
	);
}
```

## Progress Indicators

Components for showing progress.

```tsx
import { Progress } from "@cloc/ui";

function ProgressExample() {
	return (
		<Progress
			value={75} // Progress value (0-100)
			variant="default" // default, success, error
			size="medium" // small, medium, large
			showValue={true} // Show percentage
		/>
	);
}
```

### 4. Navigation

##

Collapsible content sections.

```tsx
import {
	Accordion,
	AccordionItem,
	AccordionTrigger,
	AccordionContent,
} from "@cloc/ui";

function AccordionExample() {
	return (
		<Accordion type="single" collapsible>
			<AccordionItem value="item-1">
				<AccordionTrigger>Section 1</AccordionTrigger>
				<AccordionContent>Content for section 1</AccordionContent>
			</AccordionItem>
		</Accordion>
	);
}
```

## Dropdown Menus

Contextual menus with various trigger options.

```tsx
import {
	DropdownMenu,
	DropdownMenuTrigger,
	DropdownMenuContent,
	DropdownMenuItem,
} from "@cloc/ui";

function DropdownExample() {
	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button>Open Menu</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent>
				<DropdownMenuItem>Item 1</DropdownMenuItem>
				<DropdownMenuItem>Item 2</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
```

## Theming

@cloc/ui components support customization through a theme system:

```tsx
import { ThemeProvider } from "@cloc/ui";

function App() {
	return (
		<ThemeProvider
			theme={{
				colors: {
					primary: "#007AFF",
					// ... other color values
				},
				// ... other theme values
			}}
		>
			<YourApp />
		</ThemeProvider>
	);
}
```

## Accessibility

All components are built with accessibility in mind:

- ARIA attributes are properly implemented
- Keyboard navigation is supported
- Focus management is handled appropriately
- Color contrast meets WCAG guidelines

## Best Practices

1. **Component Usage**
   - Use semantic HTML elements when possible
   - Maintain proper form labeling
   - Implement proper error handling
   - Follow accessibility guidelines
2. **Performance**
   - Lazy load components when appropriate
   - Use proper React patterns (useMemo, useCallback)
   - Implement proper event handling
3. **Theming**
   - Use theme tokens instead of hard-coded values
   - Maintain consistent spacing using theme values
   - Follow color system guidelines

## Browser Support

- Chrome (latest 2 versions)
- Firefox (latest 2 versions)
- Safari (latest 2 versions)
- Edge (latest 2 versions)
