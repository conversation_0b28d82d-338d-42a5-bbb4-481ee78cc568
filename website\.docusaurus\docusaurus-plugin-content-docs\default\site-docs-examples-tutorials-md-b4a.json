{"id": "examples-tutorials", "title": "Examples & Tutorials", "description": "Basic Setup", "source": "@site/docs/examples-tutorials.md", "sourceDirName": ".", "slug": "/examples-tutorials", "permalink": "/docs/examples-tutorials", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/examples-tutorials.md", "tags": [], "version": "current", "sidebarPosition": 7, "frontMatter": {"id": "examples-tutorials", "title": "Examples & Tutorials", "sidebar_label": "Examples & Tutorials", "sidebar_position": 7}, "sidebar": "tutorialSidebar", "previous": {"title": "API Documentations", "permalink": "/docs/api/api-docs"}}