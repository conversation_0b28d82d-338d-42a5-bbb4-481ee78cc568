import { ClocProgress, useClocContext } from "@cloc/atoms";
import { useState, useEffect } from "react";

function useProductivityMetrics() {
  const { todayTrackedTime, isRunning } = useClocContext();
  const [productivity, setProductivity] = useState(0);

  useEffect(() => {
    // Calculate productivity score based on tracked time
    const totalMinutes = todayTrackedTime.hours * 60 + todayTrackedTime.minutes;
    const targetMinutes = 8 * 60; // 8 hours target
    const score = Math.min((totalMinutes / targetMinutes) * 100, 100);
    setProductivity(score);
  }, [todayTrackedTime]);

  return {
    productivity,
    isActive: isRunning,
    todayHours: todayTrackedTime.hours,
    todayMinutes: todayTrackedTime.minutes,
  };
}

export default function ProductivityWidget() {
  const { productivity, isActive, todayHours, todayMinutes } =
    useProductivityMetrics();

  return (
    <div className="p-4 bg-white dark:bg-black rounded-lg shadow min-w-72">
      <h3 className="font-semibold mb-2">Today's Productivity</h3>
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <ClocProgress className="h-2" />
          <p className="text-sm text-gray-600 mt-3">
            {productivity.toFixed(1)}% of daily goal
          </p>
        </div>
        <div className="text-right">
          <p className="font-mono text-lg">
            {todayHours}h {todayMinutes}m
          </p>
          <p className="text-sm text-gray-600">
            {isActive ? "Active" : "Paused"}
          </p>
        </div>
      </div>
    </div>
  );
}
