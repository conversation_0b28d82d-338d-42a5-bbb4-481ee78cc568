{"id": "core-libraries/cloc-types", "title": "Cloc Types", "description": "@cloc/types is a comprehensive TypeScript definition package that provides type safety and IntelliSense support for the Cloc ecosystem. It serves as the foundation for type definitions across all Cloc components, utilities, and features.", "source": "@site/docs/core-libraries/cloc-types.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-types", "permalink": "/docs/core-libraries/cloc-types", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-types.md", "tags": [], "version": "current", "sidebarPosition": 7, "frontMatter": {"id": "cloc-types", "title": "Cloc Types", "sidebar_label": "Cloc Types", "sidebar_position": 7}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc UI", "permalink": "/docs/core-libraries/cloc-ui"}, "next": {"title": "Cloc API", "permalink": "/docs/core-libraries/cloc-api"}}