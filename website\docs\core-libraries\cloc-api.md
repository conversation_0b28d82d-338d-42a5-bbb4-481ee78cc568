---
id: cloc-api
title: Cloc API
sidebar_label: Cloc API
sidebar_position: 8
---

# @cloc/api

@cloc/api is a powerful TypeScript library that provides seamless integration with Cloc backend services. It offers a comprehensive suite of async functions for time tracking, organization management, authentication, and reporting functionalities. Built with TypeScript, it ensures type safety and excellent developer experience.

## Installation

```bash
# Using npm
npm install @cloc/api

# Using yarn
yarn add @cloc/api

# Using pnpm
pnpm add @cloc/api
```

## Core Features

### 1. Authentication & User Management

The authentication module provides async functions for managing user sessions and access tokens.

### Authentication Functions

```typescript
import { authenticateUser, validateToken, refreshToken } from "@cloc/api";

// Authenticate user
const login = async (email: string, password: string) => {
	try {
		const response = await authenticateUser({ email, password });
		return response.token;
	} catch (error) {
		console.error("Authentication failed:", error);
		return null;
	}
};

// Validate existing token
const validateSession = async (token: string) => {
	const isValid = await validateToken(token);
	return isValid;
};
```

### User Management

```typescript
import { getUserProfile, updateUserProfile } from "@cloc/api";

// Fetch user profile
const getProfile = async (token: string) => {
	const profile = await getUserProfile(token);
	return profile;
};

// Update user data
const updateProfile = async (token: string, userData: Partial<IUser>) => {
	const updated = await updateUserProfile(token, userData);
	return updated;
};
```

### 2. Time Tracking

The time tracking module provides functions for managing timer states and logs.

```typescript
import { startTimer, stopTimer, getTimerStatus } from "@cloc/api";

// Start a timer
const beginTracking = async (token: string, options: TimerOptions) => {
	const timer = await startTimer(token, options);
	return timer;
};

// Stop active timer
const endTracking = async (token: string) => {
	const result = await stopTimer(token);
	return result;
};

// Get current timer status
const checkStatus = async (token: string) => {
	const status = await getTimerStatus(token);
	return status;
};
```

### 3. Organization Management

Functions for managing teams, projects, and organization data.

```typescript
import { getTeams, getProjects, getMembers } from "@cloc/api";

// Fetch organization teams
const fetchTeams = async (token: string, orgId: string) => {
	const teams = await getTeams(token, orgId);
	return teams;
};

// Get organization projects
const fetchProjects = async (token: string, orgId: string) => {
	const projects = await getProjects(token, orgId);
	return projects;
};
```

## Error Handling

All API functions include proper error handling and typing:

```typescript
import { ApiError, handleApiError } from "@cloc/api";

try {
	const result = await someApiFunction(token);
	return result;
} catch (error) {
	if (error instanceof ApiError) {
		handleApiError(error);
	}
	throw error;
}
```

## Type Definitions

The library includes comprehensive TypeScript definitions:

```typescript
interface TimerOptions {
	projectId?: string;
	taskId?: string;
	description?: string;
}

interface TimerStatus {
	isRunning: boolean;
	startTime: string;
	duration: number;
	projectId?: string;
	taskId?: string;
}
```

## Compatibility

- Node.js: ^16.x - ^20.x
- TypeScript: ^4.x - ^5.x
- Supports both ESM and CommonJS

## Best Practices

1. **Token Management**

   - Store tokens securely
   - Implement proper token refresh logic
   - Handle token expiration gracefully

2. **Error Handling**

   - Always wrap API calls in try-catch blocks
   - Implement proper error logging
   - Handle network failures appropriately

3. **Performance**

   - Cache responses when appropriate
   - Implement request debouncing for frequent calls
   - Use proper request cancellation

4. **TypeScript Integration**
   - Leverage provided type definitions
   - Use strict type checking
   - Implement proper error boundaries
