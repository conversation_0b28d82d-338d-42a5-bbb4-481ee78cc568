---
id: api-docs
title: API Documentations
sidebar_label: API Documentations
sidebar_position: 8
---

## 1. Authentication Endpoints

### Sign In with Email/Password

- **Endpoint**: `/auth/signin.email.password`
- **Method**: POST
- **Auth**: Not required
- **Body**:

```tsx
interface SignInBody {
	email: string;
	password: string;
	includeTeams?: boolean;
}
```

- **Response**:

```tsx
interface SignInResponse {
	token: string;
	user: IUser;
}
```

- **Sample Implementation**:

```tsx
const signIn = async (email: string, password: string) => {
	const response = await ApiCall<IAuthWithEmailPassword>({
		path: "/auth/signin.email.password",
		method: "POST",
		body: {
			email,
			password,
			includeTeams: true,
		},
	});
	return response.data;
};
```

### Login

- **Endpoint**: `/auth/login`
- **Method**: POST
- **Auth**: Not required
- **Body**:

```tsx
interface LoginBody {
	email: string;
	password: string;
}
```

- **Response**:

```tsx
interface IAuthLogin {
	token: string;
	user: IUser;
}
```

- **Sample Implementation**:

```tsx
const login = async (email: string, password: string) => {
	const response = await ApiCall<IAuthLogin>({
		path: "/auth/login",
		method: "POST",
		body: { email, password },
	});
	return response.data;
};
```

## 2. Timer Endpoints

### Get Timer Status

- **Endpoint**: `/timesheet/timer/status`
- **Method**: GET
- **Auth**: Required (Bearer token)
- **Query Parameters**:

```tsx
interface TimerStatusQuery {
	tenantId: string;
	organizationId: string;
}
```

- **Response**:

```tsx
interface ITimerStatus {
	duration: number;
	lastLog: any | null;
	running: boolean;
}
```

- **Sample Implementation**:

```tsx
const getTimerStatus = async (user: IUser, token: string) => {
	const query = qs.stringify({
		tenantId: user.tenantId,
		organizationId: user.employee?.organizationId,
	});

	const response = await ApiCall<ITimerStatus>({
		path: `/timesheet/timer/status?${query}`,
		method: "GET",
		bearer_token: token,
	});
	return response.data;
};
```

### Start Timer

- **Endpoint**: `/timesheet/timer/start`
- **Method**: POST
- **Auth**: Required (Bearer token)
- **Body**:

```tsx
interface StartTimerBody {
	tenantId: string;
	organizationId: string;
	logType: LogType;
	source: TimerSource;
	isBillable: boolean;
	currentClocState: ICurrentClocState;
}
```

- **Response**: `ITimeSlot`
- **Sample Implementation**:

```tsx
const startTimer = async (
	user: IUser,
	token: string,
	currentClocState: ICurrentClocState
) => {
	const response = await ApiCall<ITimeSlot>({
		path: "/timesheet/timer/start",
		method: "POST",
		bearer_token: token,
		body: {
			tenantId: user.tenantId,
			organizationId: user.employee?.organizationId,
			logType: LogType.TRACKED,
			source: TimerSource.CLOC,
			isBillable: true,
			...currentClocState,
		},
	});
	return response.data;
};
```

## 3. Organization Endpoints

### Get Organization Teams

- **Endpoint**: `/organization-team`
- **Method**: GET
- **Auth**: Required (Bearer token)
- **Query Parameters**:

```tsx
interface TeamsQuery {
	where: {
		organizationId: string;
		tenantId: string;
	};
	source: TimerSource;
	withLastWorkedTask: string;
	relations: string[];
}
```

- **Response**: `PaginationResponse<IOrganizationTeamList>`
- **Sample Implementation**:

```tsx
const getTeams = async (user: IUser, token: string) => {
	const query = QueryString.stringify({
		where: {
			organizationId: user.employee?.organizationId,
			tenantId: user.tenantId,
		},
		source: TimerSource.CLOC,
		withLastWorkedTask: "true",
		relations: [
			"members",
			"members.role",
			"members.employee",
			"members.employee.user",
			"createdBy",
			"projects",
			"projects.customFields.repository",
		],
	});

	const response = await ApiCall<PaginationResponse<IOrganizationTeamList>>({
		path: `/organization-team?${query}`,
		method: "GET",
		bearer_token: token,
		tenantId: user.tenantId,
	});
	return response.data;
};
```

## 4. Organization Projects Endpoints

### Get Employee's Organization Projects

- **Endpoint**: `/organization-projects/employee/{employeeId}`
- **Method**: GET
- **Auth**: Required (Bearer token)
- **Query Parameters**:

```tsx
interface ProjectsQuery {
	organizationId: string;
	tenantId: string;
	employeeId: string;
}
```

- **Response**: `IProject[]`
- **Sample Implementation**:

```tsx
const getProjects = async (user: IUser, token: string) => {
	const query = qs.stringify({
		organizationId: user.employee?.organizationId,
		tenantId: user.tenantId,
		employeeId: user.employee?.id,
	});

	const response = await ApiCall<IProject[]>({
		path: `/organization-projects/employee/${user.employee?.id}?${query}`,
		method: "GET",
		bearer_token: token,
		tenantId: user.tenantId,
	});
	return response.data;
};
```

## 5. Organization Clients Endpoints

### Get Organization Clients

- **Endpoint**: `/organization-contact/pagination`
- **Method**: GET
- **Auth**: Required (Bearer token)
- **Query Parameters**:

```tsx
interface ClientsQuery {
	where: {
		organizationId: string;
		tenantId: string;
		contactType: ContactType.CLIENT;
		members?: string[];
	};
	join: {
		alias: string;
		leftJoin: {
			members: string;
		};
	};
	relations: string[];
}
```

- **Response**: `PaginationResponse<IOrganizationContact>`
- **Sample Implementation**:

```tsx
const getClients = async (user: IUser, token: string) => {
	const query = qs.stringify({
		where: {
			organizationId: user.employee?.organizationId,
			tenantId: user.tenantId,
			contactType: ContactType.CLIENT,
			members: [user.employee?.id],
		},
		join: {
			alias: "organization_contact",
			leftJoin: {
				members: "organization_contact.members",
			},
		},
		relations: ["projects.members", "members.user", "tags", "contact"],
	});

	const response = await ApiCall<PaginationResponse<IOrganizationContact>>({
		path: `/organization-contact/pagination?${query}`,
		method: "GET",
		bearer_token: token,
		tenantId: user.tenantId,
	});
	return response.data.items;
};
```

## 6. Tasks Endpoints

### Get My Tasks

- **Endpoint**: `/tasks/me`
- **Method**: GET
- **Auth**: Required (Bearer token)
- **Query Parameters**:

```tsx
interface TasksQuery {
	where: {
		organizationId: string;
		tenantId: string;
	};
}
```

- **Response**: `PaginationResponse<ITeamTask>`
- **Sample Implementation**:

```tsx
const getMyTasks = async (user: IUser, token: string) => {
	const query = qs.stringify({
		where: {
			organizationId: user.employee?.organizationId,
			tenantId: user.tenantId,
		},
	});

	const response = await ApiCall<PaginationResponse<ITeamTask>>({
		path: `/tasks/me?${query}`,
		method: "GET",
		bearer_token: token,
		tenantId: user.tenantId,
	});
	return response.data;
};
```

## Common Types Used

```tsx
interface IUser {
	id: string;
	tenantId: string;
	email: string;
	employee?: {
		id: string;
		organizationId: string;
	};
	// ... other user properties
}

interface PaginationResponse<T> {
	items: T[];
	total: number;
}

enum ContactType {
	CLIENT = "CLIENT",
	// ... other contact types
}

enum TimerSource {
	CLOC = "CLOC",
	// ... other sources
}

enum LogType {
	TRACKED = "TRACKED",
	// ... other log types
}
```

All endpoints that require authentication should include the bearer token in the headers:

```tsx
headers: {
	Authorization: `Bearer ${token}`;
}
```

Error handling is implemented across all endpoints using the `reportError` helper function and returns `null` when an error occurs.
