---
id: cloc-atoms
title: Cloc Atoms
sidebar_label: Cloc Atoms
sidebar_position: 5
---

# @cloc/atoms

@cloc/atoms is a comprehensive React component library specializing in time tracking and data visualization. Built with TypeScript and modern React practices, it offers highly customizable components with built-in theme support, responsive design, and seamless integration capabilities.

## Installation

```bash
# Using npm
npm install @cloc/atoms

# Using yarn
yarn add @cloc/atoms

# Using pnpm
pnpm add @cloc/atoms

```

## Core Components

### 1. Timer Components

### ModernCloc

A feature-rich timer component offering a modern design with extensive customization options. It supports dark/light themes, responsive layouts, and interactive features.

```tsx
import { ModernCloc, ClocProvider } from "@cloc/atoms";

function TimerExample() {
	return (
		<ClocProvider>
			<ModernCloc
				expanded={false}
				showProgress={true}
				variant="default"
				size="default"
				separator=":"
				draggable={true}
				resizable={false}
				className="custom-class"
			/>
		</ClocProvider>
	);
}
```

This example demonstrates a ModernCloc component with common configuration options:

- `expanded`: Controls the expanded view with additional features
- `showProgress`: Displays a progress indicator
- `variant`: Determines the visual style
- `size`: Sets the component size
- `separator`: Customizes the time separator
- `draggable`: Enables drag functionality
- `resizable`: Allows size adjustment

### Variants System

ModernCloc uses class-variance-authority (cva) for styling:

```tsx
const modernClocVariants = {
	variants: {
		variant: {
			default: "",
			bordered: "border-2 border-secondaryColor",
		},
		size: {
			default: "w-[300px]",
			sm: "w-[220px] text-sm p-4",
			lg: "w-[500px] px-8",
		},
	},
};
```

This system provides:

- Two visual variants: default and bordered
- Three size options: default, small (sm), and large (lg)
- Responsive design considerations
- Consistent styling across themes

### BasicTimer

A lightweight timer component ideal for simpler implementations:

```tsx
import { BasicTimer, ClocProvider } from "@cloc/atoms";

function BasicTimerExample() {
	return (
		<ClocProvider>
			<BasicTimer
				readonly={false}
				progress={true}
				border="thick"
				rounded="small"
				format="default"
				separator=":"
			/>
		</ClocProvider>
	);
}
```

Key features:

- Multiple time format options
- Customizable separators
- Progress indicator
- Border and rounded corner variants
- Readonly mode for display-only use

### 2. Progress Components

### ClocProgress

A versatile progress indicator component:

```tsx
import { ClocProgress } from "@cloc/atoms";

function ProgressExample() {
	return <ClocProgress value={75} className="h-2" variant="default" />;
}
```

Features:

- Linear progress visualization
- Customizable height and width
- Theme-aware colors
- Smooth animations

### 3. Data Visualization Components

### BasicClocReport

A powerful data visualization component supporting multiple chart types for comprehensive time tracking analysis:

```tsx
import { BasicClocReport, ClocProvider } from "@cloc/atoms";

function ReportExample() {
	return (
		<ClocProvider>
			<BasicClocReport
				variant="default"
				size="lg"
				className="my-report"
				showProgress={true}
				draggable={false}
			>
				<BarChart />
				<LineChart />
				<AreaChart />
			</BasicClocReport>
		</ClocProvider>
	);
}
```

Key features:

- Multiple chart types in one component
- Responsive design
- Interactive data points
- Customizable layouts
- Theme-aware styling

### Chart Types

1. **BarChart Component**:

```tsx
import { BarChart } from "@cloc/atoms";

function BarChartExample() {
	return (
		<BarChart
			data={chartData}
			orientation="vertical"
			showLabels={true}
			animate={true}
		/>
	);
}
```

Features:

- Vertical and horizontal orientations
- Customizable colors and labels
- Smooth animations
- Responsive scaling

1. **LineChart & AreaChart**:

```tsx
import { LineChart, AreaChart } from "@cloc/atoms";

function ChartExample() {
	return (
		<>
			<LineChart data={timeData} showPoints={true} curved={true} />
			<AreaChart data={volumeData} gradient={true} stacked={false} />
		</>
	);
}
```

Features:

- Interactive data points
- Gradient fills
- Curved or straight lines
- Stacking support

### 4. Date & Time Components

### ClocDateRangePicker

An advanced date range selection component:

```tsx
import { ClocDateRangePicker } from "@cloc/atoms";

function DateRangeExample() {
	return (
		<ClocDateRangePicker
			defaultValue={{
				from: new Date(),
				to: new Date(),
			}}
			showFooter={true}
			className="date-range"
			disabled={false}
		/>
	);
}
```

Features:

- Flexible date range selection
- Customizable calendar display
- Preset range options
- Keyboard navigation support
- Localization support

### TimeDisplayer

A versatile time display component:

```tsx
import { TimeDisplayer } from "@cloc/atoms";

function TimeDisplayExample() {
	return (
		<TimeDisplayer
			separator=":"
			fontSize={20}
			fontWeight="bold"
			fontFamily="monospace"
			className="time-display"
		/>
	);
}
```

Features:

- Multiple time formats
- Customizable typography
- Theme integration
- Real-time updates

### 5. Context and State Management

### ClocProvider

The core context provider for all Cloc components:

```tsx
import { ClocProvider } from "@cloc/atoms";

function App() {
	return (
		<ClocProvider
			token="your-access-token"
			theme={customTheme}
			defaultState={{
				clientId: null,
				projectId: null,
				taskId: null,
			}}
		>
			{/* Your components */}
		</ClocProvider>
	);
}
```

Features:

- Centralized state management
- Theme context
- Authentication handling
- Timer state synchronization

### Performance Optimization

1. **Efficient Rendering**:

```tsx
import { memo } from "react";
import { TimeDisplayer } from "@cloc/atoms";

const MemoizedTimeDisplayer = memo(TimeDisplayer);

function OptimizedComponent() {
	return <MemoizedTimeDisplayer fontSize={20} />;
}
```

1. **Lazy Loading**:

```tsx
import { lazy, Suspense } from "react";

const ModernCloc = lazy(() => import("@cloc/atoms/modern-cloc"));

function LazyLoadExample() {
	return (
		<Suspense fallback={<LoadingSpinner />}>
			<ModernCloc />
		</Suspense>
	);
}
```

### Browser Compatibility

The library is thoroughly tested and supports:

- Chrome/Edge 90+
- Firefox 90+
- Safari 14+
- React 17.0.0+
- Next.js 12.0.0+

### TypeScript Support

Full TypeScript support with comprehensive type definitions:

```tsx
import type {
	IModernClocProps,
	IClocProgressProps,
	ITimerDisplayerProps,
} from "@cloc/atoms";
```

### Accessibility

The library follows WCAG 2.1 guidelines:

- ARIA labels and roles
- Keyboard navigation
- Screen reader support
- Color contrast compliance
