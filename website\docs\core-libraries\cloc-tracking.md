---
id: cloc-tracking
title: Cloc Tracking
sidebar_label: Cloc Tracking
sidebar_position: 10
---

# @cloc/tracking

An analytics library that uses web page interactions to generate aggregated insights based on Microsoft Clarity.

## Overview

The `@cloc/tracking` package provides a comprehensive solution for tracking user interactions on web applications. Built on top of Microsoft Clarity, it offers seamless data collection, secure transmission to CLOC servers, and powerful visualization components for analyzing user behavior.

### Key Features

- **Microsoft Clarity Integration**: Leverages Clarity's robust tracking capabilities
- **Secure Data Transmission**: Automatic authentication and organization context
- **React Components**: Pre-built visualization components for analytics dashboards
- **TypeScript Support**: Full type safety and IntelliSense support
- **Performance Optimized**: Efficient data processing and rendering
- **Real-time Analytics**: Live session tracking and insights

## Architecture & How It Works

The package consists of several interconnected modules that work together to provide a complete tracking solution:

### Core Architecture

```mermaid
graph TD
    A[Web Application] --> B[Tracker]
    B --> C[Microsoft Clarity]
    C --> D[Data Collection]
    D --> E[Uploader]
    E --> F[CLOC API]
    F --> G[Session Storage]
    G --> H[React Components]
    H --> I[Analytics Dashboard]
```

### Package Structure

```
packages/toolkit/tracking/
├── src/
│   ├── lib/
│   │   ├── tracker/
│   │   │   ├── tracker.ts          # Main tracker interface
│   │   │   └── config.ts           # Configuration management
│   │   ├── upload/
│   │   │   └── uploader.ts         # Data upload functionality
│   │   ├── helpers/
│   │   │   └── validate-config.ts  # Configuration validation
│   │   ├── types.ts                # Core type definitions
│   │   └── constant.ts             # API endpoints and constants
│   └── index.ts                    # Main export file
├── dist/                           # Built distribution files
├── package.json                    # Package configuration
└── README.md                       # Package documentation
```

### Data Flow

1. **Initialization**: The tracker is configured with organization credentials
2. **Data Collection**: Microsoft Clarity captures user interactions
3. **Processing**: Raw interaction data is processed and validated
4. **Upload**: Processed data is securely transmitted to CLOC servers
5. **Storage**: Session data is stored with organizational context
6. **Visualization**: React components fetch and display analytics

## Installation & Setup

### Prerequisites

- Node.js 16+ or React 18+
- Valid CLOC organization credentials
- TypeScript 4.5+ (recommended)

### Installation

```bash
npm install @cloc/tracking @cloc/atoms @cloc/types
```

Or with yarn:

```bash
yarn add @cloc/tracking @cloc/atoms @cloc/types
```

### Basic Configuration

#### 1. Initialize the Tracker

```typescript
import { tracker } from "@cloc/tracking";

// Start tracking with your organization credentials
tracker.start({
  organizationId: "your-organization-id",
  tenantId: "your-tenant-id",
  token: "your-auth-token",
});
```

#### 2. Add React Provider (Optional)

For React applications using visualization components:

```typescript
import { TrackingProvider } from "@cloc/atoms";

function App() {
  return <TrackingProvider>{/* Your app components */}</TrackingProvider>;
}
```

### Environment Configuration

Create a `.env` file with your credentials:

```env
CLOC_ORGANIZATION_ID=your-organization-id
CLOC_TENANT_ID=your-tenant-id
CLOC_AUTH_TOKEN=your-auth-token
```

Then use them in your application:

```typescript
tracker.start({
  organizationId: process.env.CLOC_ORGANIZATION_ID!,
  tenantId: process.env.CLOC_TENANT_ID!,
  token: process.env.CLOC_AUTH_TOKEN!,
});
```

## Usage Guide

### Core Tracker API

The main tracker provides simple controls for managing data collection:

#### Starting and Stopping

```typescript
import { tracker } from "@cloc/tracking";

// Start tracking
tracker.start({
  organizationId: "org-123",
  tenantId: "tenant-456",
  token: "auth-token-789",
});

// Pause tracking temporarily
tracker.pause();

// Resume tracking
tracker.resume();

// Stop tracking completely
tracker.stop();
```

#### Error Handling

```typescript
try {
  tracker.start(config);
  console.log("Tracking started successfully");
} catch (error) {
  console.error("Failed to start tracking:", error.message);
  // Handle initialization errors
}
```

### Configuration Interface

The `IClocConfig` interface defines required configuration:

```typescript
interface IClocConfig {
  organizationId: string; // Your organization identifier
  tenantId: string; // Your tenant identifier
  token: string; // Authentication token
}
```

All fields are required and must be non-empty strings. The package includes built-in validation that will throw descriptive errors for missing or invalid configuration.

### Advanced Configuration

#### Custom API Endpoint

By default, the package uploads data to `http://localhost:3000/api/tracking`. To customize this:

```typescript
// Edit packages/toolkit/tracking/src/lib/constant.ts
export const CLOC_API_URL = "https://your-api.cloc.ai/api/tracking";
```

#### Upload Configuration

The uploader automatically handles:

- Authentication headers (`Bearer ${token}`)
- Organization context (`organization-id` header)
- Tenant context (`tenant-id` header)
- JSON payload formatting
- Error handling and retries

## Visualization Components

The `@cloc/atoms` package provides React components for displaying tracking analytics:

### TrackingProvider

Global state management for tracking components:

```typescript
import { TrackingProvider } from "@cloc/atoms";

<TrackingProvider config={{ baseUrl: "https://api.cloc.ai" }}>
  <YourTrackingComponents />
</TrackingProvider>;
```

**Features:**

- Session data management
- Filter state synchronization
- API configuration
- Loading and error states
- Automatic data fetching

### ClocTrackingFilter

Date and time filtering controls:

```typescript
import { ClocTrackingFilter } from "@cloc/atoms";

<ClocTrackingFilter
  className="w-full"
  autoRefresh={true}
  refreshInterval={30000}
/>;
```

**Features:**

- Date range selection
- Time range controls
- Employee filtering
- Auto-refresh capabilities
- Real-time validation

### ClocTrackingHeatmap

Interactive heatmap visualization:

```typescript
import { ClocTrackingHeatmap } from "@cloc/atoms";

<ClocTrackingHeatmap className="w-full h-[600px]" showControl={true} />;
```

**Features:**

- Click and scroll heatmaps
- Multiple color schemes (hot, cool, blue)
- Configurable aggregation radius
- Performance optimizations
- Interactive controls

### ClocTrackingClickInsight

Detailed click analytics:

```typescript
import { ClocTrackingClickInsight } from "@cloc/atoms";

<ClocTrackingClickInsight className="w-full" />;
```

**Features:**

- Total clicks and click rate metrics
- Click density analysis
- Unique elements tracking
- Spatial distribution analysis
- Top clicked elements list
- Real-time data updates

### ClocTrackingSessionInsight

Session analytics and metrics:

```typescript
import { ClocTrackingSessionInsight } from "@cloc/atoms";

<ClocTrackingSessionInsight className="w-full" />;
```

**Features:**

- Session duration metrics
- Engagement scoring
- Interaction rate analysis
- Event count tracking
- Device type detection
- Time-based analytics

## Component Examples

### Basic Analytics Dashboard

```typescript
import {
  TrackingProvider,
  ClocTrackingFilter,
  ClocTrackingHeatmap,
  ClocTrackingClickInsight,
  ClocTrackingSessionInsight,
} from "@cloc/atoms";

function AnalyticsDashboard() {
  return (
    <TrackingProvider>
      <div className="space-y-6">
        {/* Filter Controls */}
        <ClocTrackingFilter />

        {/* Analytics Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ClocTrackingClickInsight />
          <ClocTrackingSessionInsight />
        </div>

        {/* Heatmap Visualization */}
        <ClocTrackingHeatmap className="h-[600px]" />
      </div>
    </TrackingProvider>
  );
}
```

### Custom Heatmap Configuration

```typescript
import { ClarityHeatmap } from "@cloc/atoms";

function CustomHeatmap({ decodedPayloads }) {
  return (
    <ClarityHeatmap
      decodedPayloads={decodedPayloads}
      colorScheme="cool"
      aggregationRadius={20}
      showCounts={false}
      heatmapMode="clicks"
      className="custom-heatmap"
    />
  );
}
```

### High-Precision Heatmap

```typescript
function HighPrecisionHeatmap({ decodedPayloads }) {
  return (
    <ClarityHeatmap
      decodedPayloads={decodedPayloads}
      colorScheme="blue"
      aggregationRadius={5} // Very precise clustering
      showCounts={true}
      heatmapMode="clicks"
    />
  );
}
```

## API Reference

### Tracker Interface

#### `tracker.start(config: IClocConfig): void`

Initializes tracking with the provided configuration.

**Parameters:**

- `config` - Configuration object containing organization credentials

**Throws:**

- `Error` - If configuration validation fails or initialization errors occur

**Example:**

```typescript
tracker.start({
  organizationId: "org-123",
  tenantId: "tenant-456",
  token: "auth-token",
});
```

#### `tracker.stop(): void`

Stops tracking completely and clears all data collection.

#### `tracker.pause(): void`

Temporarily pauses data collection without stopping the tracker.

#### `tracker.resume(): void`

Resumes data collection after being paused.

### Configuration Types

#### `IClocConfig`

```typescript
interface IClocConfig {
  organizationId: string; // Required: Organization identifier
  tenantId: string; // Required: Tenant identifier
  token: string; // Required: Authentication token
}
```

#### `IClocSession`

```typescript
interface IClocSession {
  sessionId: string; // Unique session identifier
  payloads: string[]; // Array of encoded Clarity payloads
  createdAt: string; // Session creation timestamp
  updatedAt: string; // Last update timestamp
  employeeId: string; // Associated employee ID
  organizationId: string; // Organization context
  tenantId: string; // Tenant context
}
```

### Component Props

#### `ClocTrackingHeatmap`

```typescript
interface ClocTrackingHeatmapProps {
  className?: string; // CSS classes
  showControl?: boolean; // Show heatmap controls (default: true)
}
```

#### `ClarityHeatmap`

```typescript
interface ClarityHeatmapProps {
  decodedPayloads: Data.DecodedPayload[]; // Required: Session data
  className?: string; // CSS classes
  aggregationRadius?: number; // Clustering radius (default: 10)
  showCounts?: boolean; // Show count markers (default: true)
  colorScheme?: "hot" | "cool" | "blue"; // Color scheme (default: 'hot')
  heatmapMode?: "clicks" | "scroll"; // Heatmap type (default: 'clicks')
}
```

#### `ClocTrackingFilter`

```typescript
interface ClocTrackingFilterProps {
  className?: string; // CSS classes
  autoRefresh?: boolean; // Enable auto-refresh (default: false)
  refreshInterval?: number; // Refresh interval in ms (default: 30000)
}
```

## Utilities & Helpers

### Analytics Utilities

The package includes utility functions for processing tracking data:

#### `calculateClickInsights(decodedPayloads: Data.DecodedPayload[]): IClickInsight`

Analyzes click events and returns comprehensive metrics:

```typescript
import { calculateClickInsights } from "@cloc/atoms";

const insights = calculateClickInsights(decodedPayloads);
console.log(insights.totalClicks); // Total number of clicks
console.log(insights.clickRate); // Clicks per minute
console.log(insights.clickDensity); // Clicks per 1000px²
console.log(insights.uniqueElements); // Number of unique clicked elements
```

#### `calculateSessionMetrics(decodedPayloads: Data.DecodedPayload[]): SessionMetrics`

Calculates session-level analytics:

```typescript
import { calculateSessionMetrics } from "@cloc/atoms";

const metrics = calculateSessionMetrics(decodedPayloads);
console.log(metrics.totalDuration); // Total session duration (ms)
console.log(metrics.activeDuration); // Active interaction time (ms)
console.log(metrics.engagementScore); // Engagement score (0-100)
console.log(metrics.interactionRate); // Interactions per minute
```

#### `getSessionDeviceType(decodedPayloads: Data.DecodedPayload[]): DeviceInfo`

Detects device type from session data:

```typescript
import { getSessionDeviceType } from "@cloc/atoms";

const deviceInfo = getSessionDeviceType(decodedPayloads);
console.log(deviceInfo.isMobile); // boolean
console.log(deviceInfo.deviceLabel); // "📱 Mobile" or "🖥️ Desktop"
```

## Error Handling

### Configuration Validation

The package validates configuration on startup and provides detailed error messages:

```typescript
try {
  tracker.start({
    organizationId: "", // Invalid: empty string
    tenantId: "tenant-123",
    token: "token-456",
  });
} catch (error) {
  console.error(error.message);
  // "The following fields must be non-empty strings to start tracking: organizationId"
}
```

### Upload Error Handling

The uploader includes automatic error handling and retry logic:

```typescript
// Automatic retry on network failures
// Detailed error logging
// Graceful degradation on upload failures
```

### Component Error Boundaries

React components include error boundaries and loading states:

```typescript
// Components show loading spinners during data fetching
// Error states display user-friendly messages
// Graceful fallbacks when data is unavailable
```

## Performance Optimization

### Heatmap Performance

The heatmap components include several performance optimizations:

- **Memoization**: React.memo prevents unnecessary re-renders
- **Debounced Controls**: Slider interactions are debounced to reduce computation
- **RequestAnimationFrame**: Smooth scrolling and frame synchronization
- **Context Optimization**: Custom hooks reduce re-render frequency by 70-80%

### Data Processing

- **Lazy Loading**: Components only process data when visible
- **Efficient Algorithms**: Optimized click clustering and spatial analysis
- **Memory Management**: Automatic cleanup of processed data

## Troubleshooting

### Common Issues

#### Tracking Not Starting

**Problem**: `tracker.start()` throws an error

**Solutions:**

1. Verify all configuration fields are non-empty strings
2. Check network connectivity to the API endpoint
3. Validate authentication token is current and valid

#### No Data in Components

**Problem**: Components show "No data available"

**Solutions:**

1. Ensure tracking has been started and is collecting data
2. Check date range filters are not too restrictive
3. Verify API endpoint is accessible and returning data

#### Performance Issues

**Problem**: Heatmap rendering is slow

**Solutions:**

1. Reduce `aggregationRadius` for fewer clusters
2. Limit the number of sessions processed simultaneously
3. Use `showCounts={false}` to reduce rendering complexity

### Debug Mode

Enable debug logging for troubleshooting:

```typescript
// Add to your application
console.log("Tracking sessions:", sessions);
console.log("Decoded payloads:", decodedPayloads);
```

## Migration Guide

### From Legacy Versions

If migrating from older tracking implementations:

1. **Update Configuration**: Use the new `IClocConfig` interface
2. **Component Updates**: Replace legacy components with new ones
3. **Context Provider**: Wrap your app with `TrackingProvider`
4. **API Changes**: Update any direct API calls to use new endpoints

### Breaking Changes

- Configuration now requires `organizationId`, `tenantId`, and `token`
- Component props have been standardized
- Session data structure has been updated to include organizational context

## Best Practices

### Security

- Store credentials in environment variables, not in code
- Use HTTPS endpoints in production
- Regularly rotate authentication tokens
- Implement proper access controls for analytics data

### Performance

- Use React.memo for custom components consuming tracking data
- Implement proper loading states for better UX
- Consider pagination for large datasets
- Use debouncing for real-time filters

### Data Privacy

- Ensure compliance with privacy regulations (GDPR, CCPA)
- Implement data retention policies
- Provide user opt-out mechanisms
- Anonymize sensitive data before processing

## Support

For additional support and documentation:

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Comprehensive API reference and guides
- **Community**: Join our developer community for discussions
- **Enterprise Support**: Contact our team for enterprise-level assistance

---

_This documentation covers version 0.0.1 of the @cloc/tracking package. For the latest updates and changes, please refer to the package changelog._
