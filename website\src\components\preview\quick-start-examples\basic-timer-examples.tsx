import { ClocBasic } from "@cloc/atoms";

export default function BasicTimerExamples() {
  return (
    <div className=" flex flex-col md:flex-row  gap-10">
      <div>
        Default Basic Timer
        <ClocBasic readonly={false} progress={false} />
      </div>
      <div>
        Timer with Progress
        <ClocBasic
          readonly={false}
          progress={true}
          className="w-fit max-w-[300px] border"
        />
      </div>
      <div>
        Read-only Display
        <ClocBasic readonly={true} progress={false} />
      </div>
    </div>
  );
}
