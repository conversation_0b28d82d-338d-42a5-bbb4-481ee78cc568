{"id": "introduction/quick-start-example", "title": "Quick Start Examples", "description": "This guide provides comprehensive examples for getting started with the Cloc SDK, from basic timer components to advanced analytics and tracking features.", "source": "@site/docs/introduction/quick-start-example.mdx", "sourceDirName": "introduction", "slug": "/introduction/quick-start-example", "permalink": "/docs/introduction/quick-start-example", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/introduction/quick-start-example.mdx", "tags": [], "version": "current", "sidebarPosition": 5, "frontMatter": {"id": "quick-start-example", "title": "Quick Start Examples", "sidebar_label": "Quick Start Examples", "sidebar_position": 5, "sidebar_class_name": "docs-sidebar-quick-start"}, "sidebar": "tutorialSidebar", "previous": {"title": "Installation Guide", "permalink": "/docs/introduction/installation-guide"}, "next": {"title": "Core Libraries", "permalink": "/docs/core-libraries/"}}