{"id": "core-libraries/cloc-atoms", "title": "Cloc Atoms", "description": "@cloc/atoms is a comprehensive React component library specializing in time tracking and data visualization. Built with TypeScript and modern React practices, it offers highly customizable components with built-in theme support, responsive design, and seamless integration capabilities.", "source": "@site/docs/core-libraries/cloc-atoms.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-atoms", "permalink": "/docs/core-libraries/cloc-atoms", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-atoms.md", "tags": [], "version": "current", "sidebarPosition": 5, "frontMatter": {"id": "cloc-atoms", "title": "Cloc Atoms", "sidebar_label": "Cloc Atoms", "sidebar_position": 5}, "sidebar": "tutorialSidebar", "previous": {"title": "Core Libraries", "permalink": "/docs/core-libraries/"}, "next": {"title": "Cloc UI", "permalink": "/docs/core-libraries/cloc-ui"}}