import { Cloc<PERSON>rovider } from "@cloc/atoms";
import { ThemeProvider, useTheme } from "next-themes";
import useDocusaurusContext from "@docusaurus/useDocusaurusContext";

export default function Root({ children }) {
  const { siteConfig } = useDocusaurusContext();
  const apiUrl = siteConfig?.customFields?.CLOC_API_URL as string;

  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body>
        <ThemeProvider attribute={"class"} defaultTheme="dark">
          <ClocProvider config={{ apiUrl }}>{children}</ClocProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
