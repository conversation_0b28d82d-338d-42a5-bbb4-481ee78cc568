{"id": "core-libraries/cloc-ui", "title": "Cloc UI", "description": "@cloc/ui is a comprehensive React component library that provides the foundational building blocks for creating consistent, accessible, and themeable user interfaces across Cloc applications. The library emphasizes reusability, accessibility, and modern design principles.", "source": "@site/docs/core-libraries/cloc-ui.md", "sourceDirName": "core-libraries", "slug": "/core-libraries/cloc-ui", "permalink": "/docs/core-libraries/cloc-ui", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/core-libraries/cloc-ui.md", "tags": [], "version": "current", "sidebarPosition": 6, "frontMatter": {"id": "cloc-ui", "title": "Cloc UI", "sidebar_label": "Cloc UI", "sidebar_position": 6}, "sidebar": "tutorialSidebar", "previous": {"title": "Cloc Atoms", "permalink": "/docs/core-libraries/cloc-atoms"}, "next": {"title": "Cloc Types", "permalink": "/docs/core-libraries/cloc-types"}}