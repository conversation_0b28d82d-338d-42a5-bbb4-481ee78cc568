import { TodayTimeDisplayer, useClocContext } from "@cloc/atoms";
import { ThemedButton } from "@cloc/ui";

export default function CustomTimerDisplay() {
  const { hours, minutes, seconds, isRunning, startTimer, stopTimer } =
    useClocContext();

  return (
    <div className="p-8 border dark:border-gray-600 rounded-full text-center bg-white dark:bg-black ">
      <div className="text-2xl font-mono">
        {String(hours).padStart(2, "0")}:{String(minutes).padStart(2, "0")}:
        {String(seconds).padStart(2, "0")}
      </div>
      <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
        Today: <TodayTimeDisplayer />
      </div>
      <div className="mt-2 space-x-2">
        <ThemedButton
          onClick={isRunning ? stopTimer : startTimer}
          className="w-full px-4 py-2"
        >
          {isRunning ? "Stop" : "Start"}
        </ThemedButton>
      </div>
    </div>
  );
}
