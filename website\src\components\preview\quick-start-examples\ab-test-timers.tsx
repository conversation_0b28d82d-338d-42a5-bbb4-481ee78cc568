import { ModernCloc, ClocBasic } from "@cloc/atoms";
import { useState, useEffect } from "react";

export default function ABTestTimers() {
  const [variant, setVariant] = useState<'A' | 'B'>('A');

  useEffect(() => {
    // Randomly assign users to A/B test groups
    setVariant(Math.random() > 0.5 ? 'A' : 'B');
  }, []);

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-4">
        Timer Variant: {variant}
      </h2>

      {variant === 'A' ? (
        // Variant A: Modern expanded timer
        <ModernCloc
          expanded={true}
          showProgress={true}
          variant="bordered"
          size="lg"
        />
      ) : (
        // Variant B: Compact basic timer
        <ClocBasic
          progress={true}
          readonly={false}
        />
      )}
    </div>
  );
}
