---
id: quick-start-example
title: Quick Start Examples
sidebar_label: Quick Start Examples
sidebar_position: 5
sidebar_class_name: docs-sidebar-quick-start
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import ComponentPreview from '@site/src/components/ui/component-preview';
import Preview from '@site/src/components/preview/preview';
import SimpleTimer from '@site/src/components/preview/quick-start-examples/simple-timer-example';
import ModernTimerExamples from '@site/src/components/preview/quick-start-examples/modern-timer-examples';
import BasicTimerExamples from '@site/src/components/preview/quick-start-examples/basic-timer-examples';
import CustomTimerExamples from '@site/src/components/preview/quick-start-examples/custom-timer-examples';
import CustomTimerDisplay from '@site/src/components/preview/quick-start-examples/custom-timer-display';
import AnalyticsDashboard from '@site/src/components/preview/quick-start-examples/analytics-dashboard';
import ClickAnalytics from '@site/src/components/preview/quick-start-examples/click-analytics';
import SessionAnalytics from '@site/src/components/preview/quick-start-examples/session-analytics';
import ReportDashboard from '@site/src/components/preview/quick-start-examples/report-dashboard';
import EcommerceTimeTracker from '@site/src/components/preview/quick-start-examples/ecommerce-time-tracker';
import UserAnalyticsDashboard from '@site/src/components/preview/quick-start-examples/user-analytics-dashboard';
import ABTestTimers from '@site/src/components/preview/quick-start-examples/ab-test-timers';
import ProductivityApp from '@site/src/components/preview/quick-start-examples/productivity-app';
import ThemedApp from '@site/src/components/preview/quick-start-examples/themed-app';
import CustomThemedApp from '@site/src/components/preview/quick-start-examples/custom-themed-app';
import MobileApp from '@site/src/components/preview/quick-start-examples/mobile-app';
import ProductivityWidget from '@site/src/components/preview/quick-start-examples/productivity-widget';
import AppWithErrorHandling from '@site/src/components/preview/quick-start-examples/app-with-error-handling';

# Quick Start Examples

This guide provides comprehensive examples for getting started with the Cloc SDK, from basic timer components to advanced analytics and tracking features.

:::tip Prerequisites
- React 18+
- TypeScript 4.5+
- Node.js 18+
:::

## Installation

Install the packages you need for your project:

<Tabs>
<TabItem value="npm" label="npm" default>

```bash
# Core package for time tracking, reports and analytics
npm install @cloc/atoms

# Add tracking package
npm install @cloc/tracking
```
</TabItem>
<TabItem value="yarn" label="yarn">

```bash
# Core package for time tracking, reports and analytics
yarn add @cloc/atoms

# Add tracking package
yarn add @cloc/tracking
```
</TabItem>

<TabItem value="pnpm" label="pnpm">

```bash
# Core package for time tracking, reports and analytics
pnpm add @cloc/atoms

# Add tracking package
pnpm add @cloc/tracking
```
</TabItem>

</Tabs>

## Quick Setup

### 1. Basic Provider Setup

Every Cloc application starts with the `ClocProvider` wrapper:

```tsx 
import { ClocProvider } from "@cloc/atoms";
import "@cloc/atoms/styles.css";
import "@cloc/ui/styles.css";

export default function App() {
  return (
    <ClocProvider>
      {/* Your app components */}
    </ClocProvider>
  );
}
```
### 2. Simple Timer Example

Get started with a basic timer in just a few lines:

<ComponentPreview code={`import { ModernCloc } from "@cloc/atoms";
export default function SimpleTimer() {
  return (
    <ModernCloc
      className="max-w-[300px]"
      expanded={true}
      showProgress={true}
      variant="default"
    />
  );
}`} language='tsx' >
<SimpleTimer />
</ComponentPreview>

## Core Timer Components

### ModernCloc - Advanced Timer

The flagship timer component with modern design and advanced features:

<ComponentPreview code={`
import { ModernCloc } from "@cloc/atoms";

function ModernTimerExamples() {
  return (
    <div className="flex gap-3 flex-wrap">
      {/* Compact Timer */}
      <ModernCloc
        expanded={false}
        showProgress={true}
        variant="default"
        size="sm"
        className="max-w-[200px] h-fit"
      />
      {/* Expanded Timer with Controls */}
      <ModernCloc
        expanded={false}
        showProgress={true}
        variant="bordered"
        size="default"
        resizable={true}
        className="h-fit"
      />
      {/* Large Resizable Timer */}
      <ModernCloc
        expanded={true}
        showProgress={true}
        className="h-fit"
        variant="default"
        resizable={true}
        draggable={true}
      />
    </div>
  );
}

`} language='tsx'>
<ModernTimerExamples />
</ComponentPreview>


**ModernCloc Props:**
- `expanded`: Toggle between compact and expanded views
- `showProgress`: Display a progress indicator
- `variant`: "default" | "bordered" styles
- `size`: "sm" | "default" | "lg" sizes
- `resizable`: Allow users to resize the component
- `draggable`: Enable drag functionality
- `separator`: Custom time separator (default: ":")

### ClocBasic - Essential Timer

A lightweight timer component for basic time tracking needs:

<ComponentPreview code={`import { ClocBasic } from "@cloc/atoms";

function BasicTimerExamples() {
  return (
    <div className="flex flex-wrap gap-10">
      <div>
        Default Basic Timer
        <ClocBasic readonly={false} progress={false} />
      </div>
      <div>
        Timer with Progress
        <ClocBasic
          readonly={false}
          progress={true}
          className="w-fit max-w-[300px] border"
        />
      </div>
      <div>
        Read-only Display
        <ClocBasic readonly={true} progress={false} />
      </div>
    </div>
  );
}`} language="tsx">
<BasicTimerExamples />
</ComponentPreview>

### BasicTimer - Customizable Timer

Highly customizable timer with styling options:

<ComponentPreview code={`import { BasicTimer } from "@cloc/atoms";

function CustomTimerExamples() {
  return (
    <div className="space-y-4">
      {/* Primary Theme Timer */}
      <BasicTimer
        background="primary"
        color="secondary"
        border="thick"
        icon={true}
      />

      {/* Secondary Theme Timer */}
      <BasicTimer
        background="secondary"
        color="primary"
        border="thin"
        icon={false}
      />

      {/* Destructive Theme Timer */}
      <BasicTimer
        background="destructive"
        color="primary"
        border="none"
        readonly={true}
      />
    </div>
  );
}`} language="tsx">
<CustomTimerExamples />
</ComponentPreview>

## Using Hooks for Custom Components

### useClocContext Hook

Access timer state and controls in custom components:

<ComponentPreview code={`import { TodayTimeDisplayer, useClocContext } from "@cloc/atoms";
import { ThemedButton } from "@cloc/ui";

export default function CustomTimerDisplay() {
  const { hours, minutes, seconds, isRunning, startTimer, stopTimer } = useClocContext();

  return (
    <div className="p-8 border dark:border-gray-600 rounded-full text-center bg-white dark:bg-black ">
      <div className="text-2xl font-mono">
        {String(hours).padStart(2, "0")}:{String(minutes).padStart(2, "0")}:
        {String(seconds).padStart(2, "0")}
      </div>
      <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
        Today: <TodayTimeDisplayer />
      </div>
      <div className="mt-2 space-x-2">
        <ThemedButton
          onClick={isRunning ? stopTimer : startTimer}
          className="w-full px-4 py-2"
        >
          {isRunning ? "Stop" : "Start"}
        </ThemedButton>
      </div>
    </div>
  );
}
`} language="tsx">
<CustomTimerDisplay />
</ComponentPreview>

## Analytics & Tracking Integration

### Setting Up Tracking

Initialize tracking to capture user interactions and analytics:

```tsx
import { tracker } from "@cloc/tracking";

// Initialize tracking with your credentials
tracker.start({
  organizationId: 'your-org-id',
  tenantId: 'your-tenant-id',
  token: 'your-auth-token'
});

function AppWithTracking() {
  return (
      <ClocProvider>
        {/* Your app components */}
      </ClocProvider>
  );
}
```

### Analytics Dashboard

Create a comprehensive analytics dashboard:

<ComponentPreview code={`import {
  ClocTrackingFilter,
  ClocTrackingHeatmap,
  ClocTrackingClickInsight,
  ClocTrackingSessionInsight,
} from "@cloc/atoms";

export default function AnalyticsDashboard() {
  return (
    <div className="space-y-6 ">
      {/* Filter Controls */}
      <ClocTrackingFilter autoRefresh={true} refreshInterval={30000} />

      {/* Analytics Grid */}
      <div className="grid  gap-6">
        <ClocTrackingClickInsight className="w-full" />
        <ClocTrackingSessionInsight className="w-full" />
      </div>

      {/* Heatmap Visualization */}
      <ClocTrackingHeatmap className="w-full " showControl={true} />
    </div>
  );
}
`} language="tsx">
<AnalyticsDashboard />
</ComponentPreview>

### Click Analytics Component

Detailed click analytics with insights:

<ComponentPreview code={`import { ClocTrackingClickInsight } from "@cloc/atoms";

export default function ClickAnalytics() {
    return (
      <div className="p-6 bg-white dark:bg-black rounded-lg shadow w-full">
        <h2 className="text-xl font-semibold mb-4">Click Analytics</h2>
        <ClocTrackingClickInsight className="w-full" />
      </div>
    );
}
`} language="tsx">
<ClickAnalytics />
</ComponentPreview>

### Session Analytics Component

Monitor user session metrics and engagement:

<ComponentPreview code={`import { ClocTrackingSessionInsight } from "@cloc/atoms";

function SessionAnalytics() {
  return (
    <div className="p-6 bg-white dark:bg-black rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4">Session Insights</h2>
      <ClocTrackingSessionInsight className="w-full" />
    </div>
  );
}`} language="tsx">
<SessionAnalytics />
</ComponentPreview>

## Data Visualization & Reports

### BasicClocReport - Comprehensive Charts

Create powerful data visualizations with multiple chart types:

<ComponentPreview code={`import { BasicClocReport } from "@cloc/atoms";

export default function ReportDashboard() {
  return (
    <div className="flex flex-col gap-6">
      {/* Bar Charts */}
      <BasicClocReport type="bar-vertical" variant="default" size="default" />

      <BasicClocReport type="bar" variant="bordered" />

      {/* Time Series Charts */}
      <BasicClocReport type="line" variant="default" size="default" />

      <BasicClocReport type="area" variant="default" size="default" />

      {/* Advanced Visualizations */}
      <BasicClocReport type="radar" variant="bordered" size="sm" />

      <BasicClocReport type="pie" variant="default" size="default" />
    </div>
  );
}
`} language="tsx">
<ReportDashboard />
</ComponentPreview>

**Available Chart Types:**
- `bar-vertical` & `bar`: Compare values across categories
- `line`: Show trends over time
- `area`: Display cumulative data
- `radar`: Multi-dimensional data visualization
- `pie`: Distribution analysis
- `radial`: Circular progress visualization
- `tooltip`: Interactive charts with detailed information

## Real-World Use Cases

### E-commerce Time Tracking

Track time spent on product development and customer support:

<ComponentPreview code={`import { ModernCloc, ClocProvider, tracker } from "@cloc/atoms";
import { useEffect } from "react";

function EcommerceTimeTracker() {
  useEffect(() => {
    // Initialize tracking for e-commerce analytics
    tracker.start({
      organizationId: 'ecommerce-org-123',
      tenantId: 'tenant-456',
      token: process.env.CLOC_TOKEN
    });
  }, []);

  return (
    <ClocProvider>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Product Development Timer</h1>

        {/* Development Time Tracker */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 border dark:border-gray-600 rounded-lg">
            <h3 className="font-semibold mb-3">Frontend Development</h3>
            <ModernCloc
              expanded={true}
              showProgress={true}
              variant="bordered"
              size="default"
            />
          </div>

          <div className="p-4 border dark:border-gray-600 rounded-lg">
            <h3 className="font-semibold mb-3">Backend Development</h3>
            <ModernCloc
              expanded={true}
              showProgress={true}
              variant="default"
              size="default"
            />
          </div>
        </div>
      </div>
    </ClocProvider>
  );
}
`} language='tsx' >
<EcommerceTimeTracker />
 </ComponentPreview>


### User Analytics Dashboard

Monitor user behavior and productivity metrics:

<ComponentPreview language='tsx' code={`import {
  ClocTrackingFilter,
  ClocTrackingClickInsight,
  ClocTrackingSessionInsight,
  ClocTrackingHeatmap,
} from "@cloc/atoms";

export default function UserAnalyticsDashboard() {
  return (
    <div className=" bg-gray-50 dark:bg-gray-950 p-2">
      <div className="max-w-7xl mx-auto flex flex-col gap-4">
        <h1 className="text-3xl font-bold">User Analytics Dashboard</h1>

        {/* Filter Section */}
        <ClocTrackingFilter autoRefresh={true} refreshInterval={60000} />

        {/* Analytics */}
        <ClocTrackingClickInsight />
        <ClocTrackingSessionInsight />

        {/* Heatmap Section */}
        <ClocTrackingHeatmap className="w-full" showControl={true} />
      </div>
    </div>
  );
}
`} >
<UserAnalyticsDashboard />
</ComponentPreview>

### A/B Testing with Timer Components

Test different timer configurations to optimize user engagement:

<ComponentPreview code={`import { ModernCloc, ClocBasic } from "@cloc/atoms";
import { useState, useEffect } from "react";

function ABTestTimers() {
  const [variant, setVariant] = useState<'A' | 'B'>('A');

  useEffect(() => {
    // Randomly assign users to A/B test groups
    setVariant(Math.random() > 0.5 ? 'A' : 'B');
  }, []);

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-4">
        Timer Variant: {variant}
      </h2>

      {variant === 'A' ? (
        // Variant A: Modern expanded timer
        <ModernCloc
          expanded={true}
          showProgress={true}
          variant="bordered"
          size="lg"
        />
      ) : (
        // Variant B: Compact basic timer
        <ClocBasic
          progress={true}
          readonly={false}
        />
      )}
    </div>
  );
}`} language="tsx">
<ABTestTimers />
</ComponentPreview>

### Productivity Monitoring

Track team productivity with comprehensive time analytics:

<ComponentPreview language='tsx' code={`
import {
  ModernCloc,
  BasicClocReport,
  useClocContext
} from "@cloc/atoms";

function ProductivityMonitor() {
  const { todayTrackedTime, isRunning } = useClocContext();

  return (
    <div className="max-w-6xl mx-auto p-3">
      <div className="flex flex-col gap-6">
        {/* Active Timer */}

        <div className="bg-white dark:bg-gray-900 rounded-lg shadow p-6">
          <h3 className="font-semibold mb-4">Current Session</h3>
          <ModernCloc
            expanded={true}
            showProgress={true}
            variant="default"
            size="default"
          />

          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-950 rounded">
            <p className="text-sm text-gray-600">
              Status: {isRunning ? "Active" : "Paused"}
            </p>
            <p className="text-sm text-gray-600">
              Today: {todayTrackedTime.hours}h {todayTrackedTime.minutes}m
            </p>
          </div>
        </div>

        {/* Analytics Charts */}

        <div className="flex flex-col gap-4">
          <BasicClocReport type="line" size="sm" />
          <BasicClocReport type="bar-vertical" size="sm" />
          <BasicClocReport type="area" size="sm" />
          <BasicClocReport type="pie" size="sm" />
        </div>
      </div>
    </div>
  );
}

export default function ProductivityApp() {
  return <ProductivityMonitor />;
}
`} >
<ProductivityApp />
</ComponentPreview>


## Theme Support & Customization

Cloc supports 2 kinds of themes :
#### 1. App theme : Dark and light automatic theme switching:
This theme apply to the whole app, and is automatically switched based on the user's system theme, also it can be switched using `ThemeToggle` component from SDK.

<ComponentPreview code={`import { ThemeToggle, ModernCloc, BasicClocReport } from "@cloc/atoms";

export default function ThemedApp() {
  return (
    <div className="flex bg-background text-foreground">
      {/* Theme Toggle Button */}
      <div className="p-4  max-w-fit justify-end">
        <ThemeToggle />
      </div>

      {/* Components automatically adapt to theme */}
      <div className=" p-3 space-y-6">
        <ModernCloc
          className="max-w-[300px]"
          expanded={true}
          showProgress={true}
          variant="bordered"
        />
        <BasicClocReport type="line" variant="default" />
      </div>
    </div>
  );
}
`} language="tsx">
<ThemedApp />
</ComponentPreview>

#### 2. Cloc theme : Customizable color and font themes
This theme apply to Cloc SDK components. You can either import pre-defined themes of create your own. Also you can switch Cloc predifined themes using `ClocThemeToggle` component.

<ComponentPreview code={`
import {
    ClocProvider,
    ModernCloc,
    ClocThemeToggle,
    BasicClocReport,
} from "@cloc/atoms";

const customTheme = {
    colors: {
      textColor: "black",
      backgroundColor: "white",
      mainColor: "linear-gradient(135deg, blue 0%, red 100%)",
      borderColor: "blue",
    },
};

export default function CustomThemedApp() {
    return (
        <ClocProvider theme={customTheme}>
          <div className="flex flex-col gap-3  p-3">
            <div className="flex flex-col gap-2">
              Try pre-defined theme:
              <ClocThemeToggle />
            </div>
            <ModernCloc expanded={false} showProgress={true} variant="bordered" />
          </div>
          <BasicClocReport />
        </ClocProvider>
    );
}
`} language="tsx">
<CustomThemedApp />
</ComponentPreview>

## Framework Integrations

### Next.js Integration

Complete Next.js setup with SSR support:

```tsx
// pages/_app.tsx
import type { AppProps } from 'next/app';
import { ClocProvider } from '@cloc/atoms';
import { tracker } from '@cloc/tracking';
import '@cloc/atoms/styles.css';
import '@cloc/ui/styles.css';

// Initialize tracking
if (typeof window !== 'undefined') {
  tracker.start({
    organizationId: process.env.NEXT_PUBLIC_CLOC_ORG_ID!,
    tenantId: process.env.NEXT_PUBLIC_CLOC_TENANT_ID!,
    token: process.env.NEXT_PUBLIC_CLOC_TOKEN!
  });
}

export default function App({ Component, pageProps }: AppProps) {
  return (
      <ClocProvider>
        <Component {...pageProps} />
      </ClocProvider>
  );
}
```

```tsx
// pages/dashboard.tsx
import { ModernCloc, ClocTrackingHeatmap } from '@cloc/atoms';
import { GetServerSideProps } from 'next';

interface DashboardProps {
  userToken: string;
}

export default function Dashboard({ userToken }: DashboardProps) {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Time Tracking Dashboard</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Active Timer</h2>
          <ModernCloc
            expanded={true}
            showProgress={true}
            variant="bordered"
          />
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">User Analytics</h2>
          <ClocTrackingHeatmap className="h-[400px]" />
        </div>
      </div>
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  // Server-side authentication logic
  const userToken = context.req.cookies.authToken || '';

  return {
    props: {
      userToken
    }
  };
};
```

### Builder.io Integration

Register Cloc components for visual page building:

```tsx
import { Builder } from "@builder.io/react";
import { ModernCloc, ClocBasic, BasicClocReport } from "@cloc/atoms";

// Register ModernCloc component
Builder.registerComponent(ModernCloc, {
  name: "Modern Timer",
  inputs: [
    { name: "expanded", type: "boolean", defaultValue: false },
    { name: "showProgress", type: "boolean", defaultValue: true },
    { name: "variant", type: "string", enum: ["default", "bordered"], defaultValue: "default" },
    { name: "size", type: "string", enum: ["sm", "default", "lg"], defaultValue: "default" },
    { name: "resizable", type: "boolean", defaultValue: false },
    { name: "draggable", type: "boolean", defaultValue: false }
  ],
});

// Register ClocBasic component
Builder.registerComponent(ClocBasic, {
  name: "Basic Timer",
  inputs: [
    { name: "readonly", type: "boolean", defaultValue: false },
    { name: "progress", type: "boolean", defaultValue: false }
  ],
});

// Register BasicClocReport component
Builder.registerComponent(BasicClocReport, {
  name: "Cloc Report Chart",
  inputs: [
    {
      name: "type",
      type: "string",
      enum: ["bar", "bar-vertical", "line", "area", "pie", "radar", "radial", "tooltip"],
      defaultValue: "bar-vertical"
    },
    { name: "variant", type: "string", enum: ["default", "bordered"], defaultValue: "default" },
    { name: "size", type: "string", enum: ["sm", "default", "lg"], defaultValue: "default" }
  ],
});
```

### React Native Integration

Use Cloc components in React Native applications:

<ComponentPreview code={`import { ClocProvider, ModernCloc } from '@cloc/atoms';
import { View, Text } from 'react-native';

export default function MobileApp() {
    return (
      <ClocProvider>
        <View style={{ flex: 1, padding: 20 }}>
          <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
            Mobile Time Tracker
          </Text>

          <ModernCloc
            expanded={true}
            showProgress={true}
            variant="default"
            size="default"
          />
        </View>
      </ClocProvider>
    );
}`} language="tsx">
<MobileApp />
</ComponentPreview>

## Advanced Examples

### Custom Hook Integration

Create custom hooks for specific business logic:

<ComponentPreview language='tsx' code={`import { ClocProgress, useClocContext } from "@cloc/atoms";
import { useState, useEffect } from "react";

function useProductivityMetrics() {
    const { todayTrackedTime, isRunning } = useClocContext();
    const [productivity, setProductivity] = useState(0);

    useEffect(() => {
      // Calculate productivity score based on tracked time
      const totalMinutes = todayTrackedTime.hours * 60 + todayTrackedTime.minutes;
      const targetMinutes = 8 * 60; // 8 hours target
      const score = Math.min((totalMinutes / targetMinutes) * 100, 100);
      setProductivity(score);
    }, [todayTrackedTime]);

    return {
      productivity,
      isActive: isRunning,
      todayHours: todayTrackedTime.hours,
      todayMinutes: todayTrackedTime.minutes,
    };
}

export default function ProductivityWidget() {
    const { productivity, isActive, todayHours, todayMinutes } = useProductivityMetrics();

    return (
      <div className="p-4 bg-white dark:bg-black rounded-lg shadow min-w-72">
        <h3 className="font-semibold mb-2">Today's Productivity</h3>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <ClocProgress className="h-2" />
            <p className="text-sm text-gray-600 mt-3">
              {productivity.toFixed(1)}% of daily goal
            </p>
          </div>
          <div className="text-right">
            <p className="font-mono text-lg">
              {todayHours}h {todayMinutes}m
            </p>
            <p className="text-sm text-gray-600">
              {isActive ? "Active" : "Paused"}
            </p>
          </div>
        </div>
      </div>
    );
}
`} >
<ProductivityWidget />
 </ComponentPreview>

### Error Handling & Loading States

Implement robust error handling and loading states:

<ComponentPreview language='tsx' code={`import { ModernCloc, useClocContext } from "@cloc/atoms";
import { useState, useEffect } from "react";

function RobustTimerComponent() {
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    const { isRunning, startTimer, stopTimer, loadings } = useClocContext();

    useEffect(() => {
      // Simulate initialization
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 2000);

      return () => clearTimeout(timer);
    }, []);

    const handleTimerAction = async () => {
      try {
        setError(null);
        if (isRunning) {
          await stopTimer();
        } else {
          await startTimer();
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    };

    if (isLoading || loadings.timerStatusLoading) {
      return (
        <div className="p-6 text-center">
          <p className="mt-2 text-gray-600">Loading timer...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-800 font-semibold">Error</h3>
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => setError(null)}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <ModernCloc
          expanded={true}
          className="min-w-[300px]"
          showProgress={true}
        />

        <button
          onClick={handleTimerAction}
          disabled={loadings.timerStatusLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loadings.timerStatusLoading
            ? "Processing..."
            : isRunning
            ? "Stop"
            : "Start"}
        </button>
      </div>
    );
}

export default function AppWithErrorHandling() {
    return (
      <div className="max-w-4xl mx-auto p-3">
        <h1 className="text-2xl font-bold mb-6">Robust Timer App</h1>
        <RobustTimerComponent />
      </div>
    );
}
`} >
<AppWithErrorHandling />
</ComponentPreview>

## Best Practices

### Performance Optimization

Optimize your Cloc implementation for better performance:

```tsx
import {
  ModernCloc,
  ClocProvider,
  useClocContext
} from '@cloc/atoms';
import { memo, useMemo, useCallback } from 'react';

// Memoize expensive components
const MemoizedTimer = memo(ModernCloc);

// Optimize context usage
const OptimizedTimerDisplay = memo(() => {
  const { hours, minutes, seconds, isRunning } = useClocContext();

  // Memoize formatted time to prevent unnecessary re-renders
  const formattedTime = useMemo(() => {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }, [hours, minutes, seconds]);

  return (
    <div className="font-mono text-2xl">
      {formattedTime}
      <span className={`ml-2 ${isRunning ? 'text-green-500' : 'text-red-500'}`}>
        {isRunning ? '●' : '○'}
      </span>
    </div>
  );
});

function PerformantApp() {
  // Memoize callbacks to prevent unnecessary re-renders
  const handleTimerComplete = useCallback(() => {
    console.log('Timer completed');
  }, []);

  return (
    <ClocProvider>
      <div className="p-6 space-y-6">
        <OptimizedTimerDisplay />

        <MemoizedTimer
          expanded={true}
          showProgress={true}
          variant="default"
          size="default"
        />
      </div>
    </ClocProvider>
  );
}
```

### Security Considerations

Implement secure practices for token management:

```tsx
import { tracker } from '@cloc/tracking';
import { ClocProvider } from '@cloc/atoms';
import { useEffect, useState } from 'react';

function SecureApp() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Secure token retrieval
    const initializeSecurely = async () => {
      try {
        // Get token from secure storage (not localStorage for sensitive data)
        const token = await getSecureToken();

        if (token) {
          tracker.start({
            organizationId: process.env.NEXT_PUBLIC_CLOC_ORG_ID!,
            tenantId: process.env.NEXT_PUBLIC_CLOC_TENANT_ID!,
            token: token
          });
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Authentication failed:', error);
      }
    };

    initializeSecurely();
  }, []);

  // Secure token retrieval function
  const getSecureToken = async (): Promise<string | null> => {
    // Implement your secure token retrieval logic
    // This could be from httpOnly cookies, secure storage, etc.
    const response = await fetch('/api/auth/token', {
      credentials: 'include'
    });

    if (response.ok) {
      const data = await response.json();
      return data.token;
    }

    return null;
  };

  if (!isAuthenticated) {
    return (
      <div className="p-6 text-center">
        <p>Authenticating...</p>
      </div>
    );
  }

  return (
    <ClocProvider>
      {/* Your authenticated app content */}
    </ClocProvider>
  );
}
```

:::tip Next Steps
- Explore the [API Reference](/docs/api/api-reference) for detailed component documentation
:::

:::warning Important Notes
- Always use environment variables for sensitive configuration
- Implement proper error boundaries in production applications
- Test timer functionality across different browsers and devices
- Monitor performance with React DevTools when using multiple timers
:::

## Summary

This guide covered:

- ✅  **Basic Setup**: ClocProvider configuration and simple timer usage 
- ✅  **Timer Components**: ModernCloc, ClocBasic, and BasicTimer variations 
- ✅  **Analytics Integration**: Tracking setup and analytics components
- ✅  **Data Visualization**: Charts and reports with BasicClocReport
- ✅  **Real-World Examples**: E-commerce, analytics, A/B testing scenarios
- ✅  **Framework Integration**: Next.js, Builder.io, and React Native
- ✅  **Advanced Patterns**: Custom hooks, TypeScript, and error handling
- ✅  **Best Practices**: Performance optimization and security considerations

The Cloc SDK provides a comprehensive solution for time tracking and user analytics. Start with the basic examples and gradually incorporate more advanced features as your application grows.
