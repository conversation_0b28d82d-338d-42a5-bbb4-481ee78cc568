{"help.message": "Need help?", "help.description": "This project is maintained by a dedicated group of people.", "home.message": "Ever® Cloc", "home.description": "Track the progress of your teams' work in real time", "homeActionLink.message": "Start By Reading an Introduction", "theme.ErrorPageContent.title": "This page crashed.", "theme.BackToTopButton.buttonAriaLabel": "Scroll back to top", "theme.blog.archive.title": "Archive", "theme.blog.archive.description": "Archive", "theme.blog.paginator.navAriaLabel": "Blog list page navigation", "theme.blog.paginator.newerEntries": "Newer entries", "theme.blog.paginator.olderEntries": "Older entries", "theme.blog.post.paginator.navAriaLabel": "Blog post page navigation", "theme.blog.post.paginator.newerPost": "Newer post", "theme.blog.post.paginator.olderPost": "Older post", "theme.tags.tagsPageLink": "View all tags", "theme.docs.breadcrumbs.navAriaLabel": "Breadcrumbs", "theme.docs.DocCard.categoryDescription.plurals": "1 item|{count} items", "theme.colorToggle.ariaLabel": "Switch between dark and light mode (currently {mode})", "theme.colorToggle.ariaLabel.mode.dark": "dark mode", "theme.colorToggle.ariaLabel.mode.light": "light mode", "theme.docs.paginator.navAriaLabel": "Docs pages", "theme.docs.paginator.previous": "Previous", "theme.docs.paginator.next": "Next", "theme.docs.versionBadge.label": "Version: {versionLabel}", "theme.docs.tagDocListPageTitle.nDocsTagged": "One doc tagged|{count} docs tagged", "theme.docs.tagDocListPageTitle": "{nDocsTagged} with \"{tagName}\"", "theme.common.editThisPage": "Edit this page", "theme.docs.versions.unreleasedVersionLabel": "This is unreleased documentation for {siteTitle} {versionLabel} version.", "theme.docs.versions.unmaintainedVersionLabel": "This is documentation for {siteTitle} {versionLabel}, which is no longer actively maintained.", "theme.docs.versions.latestVersionSuggestionLabel": "For up-to-date documentation, see the {latestVersionLink} ({versionLabel}).", "theme.docs.versions.latestVersionLinkLabel": "latest version", "theme.common.headingLinkTitle": "Direct link to {heading}", "theme.lastUpdated.atDate": " on {date}", "theme.lastUpdated.byUser": " by {user}", "theme.lastUpdated.lastUpdatedAtBy": "Last updated{atDate}{byUser}", "theme.NotFound.title": "Page Not Found", "theme.navbar.mobileVersionsDropdown.label": "Versions", "theme.tags.tagsListLabel": "Tags:", "theme.admonition.caution": "caution", "theme.admonition.danger": "danger", "theme.admonition.info": "info", "theme.admonition.note": "note", "theme.admonition.tip": "tip", "theme.admonition.warning": "warning", "theme.AnnouncementBar.closeButtonAriaLabel": "Close", "theme.blog.sidebar.navAriaLabel": "Blog recent posts navigation", "theme.CodeBlock.wordWrapToggle": "Toggle word wrap", "theme.CodeBlock.copied": "<PERSON>pied", "theme.CodeBlock.copyButtonAriaLabel": "Copy code to clipboard", "theme.CodeBlock.copy": "Copy", "theme.DocSidebarItem.expandCategoryAriaLabel": "Expand sidebar category '{label}'", "theme.DocSidebarItem.collapseCategoryAriaLabel": "Collapse sidebar category '{label}'", "theme.NavBar.navAriaLabel": "Main", "theme.NotFound.p1": "We could not find what you were looking for.", "theme.NotFound.p2": "Please contact the owner of the site that linked you to the original URL and let them know their link is broken.", "theme.TOCCollapsible.toggleButtonLabel": "On this page", "theme.navbar.mobileLanguageDropdown.label": "Languages", "theme.blog.post.readMore": "Read more", "theme.blog.post.readMoreLabel": "Read more about {title}", "theme.blog.post.readingTime.plurals": "One min read|{readingTime} min read", "theme.docs.sidebar.collapseButtonTitle": "Collapse sidebar", "theme.docs.sidebar.collapseButtonAriaLabel": "Collapse sidebar", "theme.docs.breadcrumbs.home": "Home page", "theme.docs.sidebar.navAriaLabel": "Docs sidebar", "theme.docs.sidebar.closeSidebarButtonAriaLabel": "Close navigation bar", "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": "← Back to main menu", "theme.docs.sidebar.toggleSidebarButtonAriaLabel": "Toggle navigation bar", "theme.docs.sidebar.expandButtonTitle": "Expand sidebar", "theme.docs.sidebar.expandButtonAriaLabel": "Expand sidebar", "theme.SearchBar.noResultsText": "No results", "theme.SearchBar.seeAllOutsideContext": "See all results outside \"{context}\"", "theme.SearchBar.searchInContext": "See all results within \"{context}\"", "theme.SearchBar.seeAll": "See all results", "theme.SearchBar.label": "Search", "theme.SearchPage.existingResultsTitle": "Search results for \"{query}\"", "theme.SearchPage.emptyResultsTitle": "Search the documentation", "theme.SearchPage.searchContext.everywhere": "Everywhere", "theme.SearchPage.documentsFound.plurals": "1 document found|{count} documents found", "theme.SearchPage.noResultsText": "No documents were found", "theme.blog.post.plurals": "One post|{count} posts", "theme.blog.tagTitle": "{nPosts} tagged with \"{tagName}\"", "theme.blog.author.pageTitle": "{authorName} - {nPosts}", "theme.blog.authorsList.pageTitle": "Authors", "theme.blog.authorsList.viewAll": "View all authors", "theme.blog.author.noPosts": "This author has not written any posts yet.", "theme.contentVisibility.unlistedBanner.title": "Unlisted page", "theme.contentVisibility.unlistedBanner.message": "This page is unlisted. Search engines will not index it, and only users having a direct link can access it.", "theme.contentVisibility.draftBanner.title": "Draft page", "theme.contentVisibility.draftBanner.message": "This page is a draft. It will only be visible in dev and be excluded from the production build.", "theme.ErrorPageContent.tryAgain": "Try again", "theme.common.skipToMainContent": "Skip to main content", "theme.tags.tagsPageTitle": "Tags"}