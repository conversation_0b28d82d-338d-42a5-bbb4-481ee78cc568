{"id": "components/timer", "title": "Timer Components", "description": "The Timer Components library provides a comprehensive suite of customizable timing solutions for React and Next.js applications. Built with TypeScript and modern React patterns, these components offer flexibility, performance, and seamless integration capabilities.", "source": "@site/docs/components/timer.md", "sourceDirName": "components", "slug": "/components/timer", "permalink": "/docs/components/timer", "draft": false, "unlisted": false, "editUrl": "https://github.com/ever-co/ever-cloc/tree/main/docs/components/timer.md", "tags": [], "version": "current", "sidebarPosition": 6, "frontMatter": {"id": "timer", "title": "Timer Components", "sidebar_label": "Timer Components", "sidebar_position": 6}, "sidebar": "tutorialSidebar", "previous": {"title": "Components Configuration", "permalink": "/docs/components/"}, "next": {"title": "Reports", "permalink": "/docs/components/reports"}}