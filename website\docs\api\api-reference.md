---
id: api-reference
title: API Reference
sidebar_label: API Reference
sidebar_position: 6
---

## Core Components

### ClocProvider

The `ClocProvider` is a powerful context provider that serves as the foundation for the Cloc ecosystem. It manages global state, authentication, timer functionality, and theming while providing seamless integration with your React application.

### Props Interface

```tsx
interface ClocProviderProps {
	/** Authentication token for API requests */
	token?: string;

	/** Theme UI compatible theme configuration */
	theme?: Theme<{}>;

	/** Child components */
	children?: React.ReactNode;
}
```

### Basic Usage

This example demonstrates how to wrap your application with the ClocProvider:

```tsx
import { ClocProvider, defaultTheme } from "@cloc/atoms";

function App() {
	return (
		<ClocProvider token="your-auth-token" theme={defaultTheme}>
			<YourApp />
		</ClocProvider>
	);
}
```

The ClocProvider integrates with Theme UI for consistent theming and provides access to timer functionality, user authentication, and other core features.

### Context API

The `useClocContext` hook provides access to the global state and methods. It's implemented using Jotai for efficient state management.

```tsx
interface IClocContext {
	// Timer Controls
	start: () => void;
	pause: () => void;
	startTimer: () => Promise<void>;
	stopTimer: () => Promise<void>;
	isRunning: boolean;

	// Time Values
	hours: number;
	minutes: number;
	seconds: number;
	totalSeconds: number;

	// Theme & Styling
	selectedFont: string;
	fontOptions: FontOption[];
	setSelectedFont: React.Dispatch<React.SetStateAction<string>>;

	// Data
	defaultData: { [key: string]: string | number }[];
	config: ChartConfig;
	todayTrackedTime: TimeValues;
	members: OT_Member[] | undefined;
}
```

### Context Usage Example

Here's how to use the context in a component:

```tsx
import { useClocContext } from "@cloc/atoms";

function TimeTracker() {
	const {
		hours,
		minutes,
		seconds,
		start,
		pause,
		isRunning,
		loadings: { timerStatusLoading },
	} = useClocContext();

	return (
		<div>
			<div>
				{timerStatusLoading ? (
					<LoaderCircle className="animate-spin" />
				) : (
					<span>{`${hours}:${minutes}:${seconds}`}</span>
				)}
			</div>
			<button onClick={isRunning ? pause : start}>
				{isRunning ? "Pause" : "Start"}
			</button>
		</div>
	);
}
```

This example shows how to implement a basic time tracker with loading states.

### UI Components

### ClocButton

A versatile button component designed specifically for time tracking actions:

```tsx
import { ClocButton } from "@cloc/atoms";

function TimerControls() {
	return (
		<ClocButton
			variant="default"
			size="default"
			// Automatically handles start/stop based on context
		/>
	);
}
```

The ClocButton integrates with the ClocContext to handle timer states and provides visual feedback for different states.

### Chart Components

The library includes various chart types for visualizing time tracking data:

```tsx
import { ClocChart } from "@cloc/atoms";

function TimeReport() {
	return (
		<ClocChart
			type="bar"
			className="min-w-[400px]"
			// Data and config handled by context
		/>
	);
}
```

Supported chart types include:

- Bar (horizontal/vertical)
- Line
- Area
- Radar
- Radial
- Tooltip

### Modern Timer Components

The library provides several timer variants:

```tsx
import { ModernCloc } from "@cloc/atoms";

function EnhancedTimer() {
	return (
		<ModernCloc
			expanded={true}
			showProgress={true}
			draggable={true}
			resizable={true}
			variant="default"
			size="lg"
		/>
	);
}
```

This modern timer component includes features like:

- Expandable view
- Progress indication
- Drag & drop functionality
- Resizable container
- Multiple size variants

### Compatibility

The library is tested and compatible with:

- React 18.x and above
- Next.js 13.x and above (including App Router)
- TypeScript 4.x and above

### Best Practices

1. **Context Usage**
   - Always wrap your app with ClocProvider at the highest appropriate level
   - Use context selectively to avoid unnecessary re-renders
   - Implement proper error boundaries for API operations
2. **Performance**
   - Utilize the built-in loading states for better UX
   - Take advantage of the automatic theme integration
   - Use the provided hooks for consistent state management
3. **Theming**
   - Extend the default theme for consistent styling
   - Use the provided theme tokens for better maintainability
   - Implement dark mode using the built-in theme system
4. **TypeScript**
   - Leverage the provided type definitions
   - Use strict type checking for better reliability
   - Extend interfaces when needed for custom functionality
